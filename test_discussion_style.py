#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced discussion-style conversation capabilities
of the Magonia agricultural assistant.
"""

import os
import sys
from magonia.gpt4o_direct import GPT4oDirectTools

def test_discussion_flow():
    """Test the discussion-style conversation flow."""
    
    # Initialize the assistant
    assistant = GPT4oDirectTools()
    
    # Simulate a conversation with multiple exchanges
    conversation_history = ""
    user_id = "test_user_123"
    
    print("=== Testing Discussion-Style Conversation ===\n")
    
    # Test 1: Initial greeting and introduction
    print("Test 1: Initial greeting")
    question1 = "Hello! I'm a farmer and I'm new to using irrigation systems."
    response1 = assistant.ask(question1, conversation_history, user_id)
    print(f"User: {question1}")
    print(f"Magonia: {response1}\n")
    
    # Update conversation history
    conversation_history += f"Human: {question1}\nAI: {response1}\n"
    
    # Test 2: Follow-up about specific farming situation
    print("Test 2: Sharing farming context")
    question2 = "I have about 50 acres of tomatoes and I'm worried about water management."
    response2 = assistant.ask(question2, conversation_history, user_id)
    print(f"User: {question2}")
    print(f"Magonia: {response2}\n")
    
    # Update conversation history
    conversation_history += f"Human: {question2}\nAI: {response2}\n"
    
    # Test 3: Technical question that should use tools
    print("Test 3: Technical irrigation question")
    question3 = "What fields need irrigation today?"
    response3 = assistant.ask(question3, conversation_history, user_id)
    print(f"User: {question3}")
    print(f"Magonia: {response3}\n")
    
    # Update conversation history
    conversation_history += f"Human: {question3}\nAI: {response3}\n"
    
    # Test 4: Follow-up question building on previous context
    print("Test 4: Building on previous discussion")
    question4 = "That's helpful! Can you tell me more about soil moisture management for tomatoes?"
    response4 = assistant.ask(question4, conversation_history, user_id)
    print(f"User: {question4}")
    print(f"Magonia: {response4}\n")
    
    # Update conversation history
    conversation_history += f"Human: {question4}\nAI: {response4}\n"
    
    # Test 5: Personal question to test non-agricultural handling
    print("Test 5: Personal question")
    question5 = "How are you doing today?"
    response5 = assistant.ask(question5, conversation_history, user_id)
    print(f"User: {question5}")
    print(f"Magonia: {response5}\n")
    
    print("=== Discussion Flow Test Complete ===")
    print("\nKey features demonstrated:")
    print("1. Natural, flowing conversation style")
    print("2. Building on previous context")
    print("3. Encouraging deeper discussion")
    print("4. Maintaining agricultural focus while being conversational")
    print("5. Seamless integration of tool data into discussion")

def test_conversation_context():
    """Test the conversation context enhancement feature."""
    
    print("\n=== Testing Conversation Context Enhancement ===\n")
    
    assistant = GPT4oDirectTools()
    
    # Create a mock conversation history
    messages = [
        {"role": "user", "content": "I'm growing tomatoes and corn on my farm"},
        {"role": "assistant", "content": "That's great! Tomatoes and corn are excellent crops. How many acres do you have?"},
        {"role": "user", "content": "About 100 acres total, 60 for corn and 40 for tomatoes"},
        {"role": "assistant", "content": "That's a substantial operation! Both crops have different water needs..."},
        {"role": "user", "content": "I'm concerned about irrigation scheduling"}
    ]
    
    # Test the context enhancement
    context = assistant._enhance_conversation_context(messages, "What's the best irrigation schedule for my crops?")
    print(f"Generated conversation context: {context}")
    
    print("\n=== Context Enhancement Test Complete ===")

if __name__ == "__main__":
    # Check if OpenAI API key is set
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY environment variable not set")
        print("Please set your OpenAI API key before running this test")
        sys.exit(1)
    
    try:
        # Run the tests
        test_discussion_flow()
        test_conversation_context()
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
