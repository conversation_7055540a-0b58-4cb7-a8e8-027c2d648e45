"""
Test script for Ollama Devstral direct implementation.
This script tests the Ollama Devstral direct API integration with tool calling.
"""

import json
import time
from ollama_devstral_direct import OllamaDevstralDirectTools

def test_ollama_devstral_with_real_data():
    """Test Ollama Devstral direct with real irrigation data."""
    
    print("🌾 Testing Ollama Devstral Direct with Real Irrigation Data")
    print("=" * 60)
    
    # Initialize the Ollama Devstral tools
    ollama_tools = OllamaDevstralDirectTools(
        enable_translation=True,
        enable_context_analysis=True
    )
    
    # Test user ID (same as used in other tests)
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    
    # Test questions covering different scenarios
    test_questions = [
        "do i need to irrigate today?",
        "what fields need irrigation today?", 
        "show me all my fields",
        "which field needs the most water?",
        "combien d'eau j'ai besoin pour l'irrigation aujourd'hui?",
        "quelles sont mes parcelles ?",
        "what is the current irrigation status?",
        "show me fields with lowest soil water volume",
        "what are my irrigation requirements for the next 7 days?",
        "which fields have optimal soil moisture?"
    ]
    
    successful_tests = 0
    total_tests = len(test_questions)
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 Test {i}/{total_tests}: {question}")
        print("-" * 50)
        
        try:
            start_time = time.time()
            
            # Ask the question using Ollama Devstral direct
            response = ollama_tools.ask(
                question=question,
                chat_history="",
                user_id=user_id
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"✅ Response ({response_time:.2f}s): {response}")
            
            # Check if response is meaningful (not empty or error)
            if response and len(response.strip()) > 10 and "error" not in response.lower():
                successful_tests += 1
                print(f"✅ Test {i} PASSED")
            else:
                print(f"❌ Test {i} FAILED - Response too short or contains error")
                
        except Exception as e:
            print(f"❌ Test {i} FAILED - Exception: {str(e)}")
            
        print("-" * 50)
    
    # Summary
    print(f"\n📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {total_tests - successful_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests >= total_tests * 0.8:  # 80% success rate
        print("🎉 OVERALL RESULT: EXCELLENT - Ollama Devstral is working well!")
    elif successful_tests >= total_tests * 0.6:  # 60% success rate
        print("✅ OVERALL RESULT: GOOD - Ollama Devstral is mostly working")
    else:
        print("⚠️ OVERALL RESULT: NEEDS IMPROVEMENT - Several tests failed")

def test_conversation_flow():
    """Test conversation flow with context."""
    
    print("\n🗣️ Testing Conversation Flow with Context")
    print("=" * 60)
    
    ollama_tools = OllamaDevstralDirectTools()
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    
    # Simulate a conversation
    conversation = [
        "Hello, I'm a farmer and I need help with irrigation",
        "Do I need to irrigate today?",
        "Which field needs the most water?",
        "What about tomorrow's irrigation needs?"
    ]
    
    chat_history = ""
    
    for i, message in enumerate(conversation, 1):
        print(f"\n👤 User: {message}")
        
        try:
            response = ollama_tools.ask(
                question=message,
                chat_history=chat_history,
                user_id=user_id
            )
            
            print(f"🤖 Magonia: {response}")
            
            # Update chat history
            if chat_history:
                history_data = json.loads(chat_history) if chat_history.startswith('[') else []
            else:
                history_data = []
            
            history_data.extend([
                {"role": "user", "content": message},
                {"role": "assistant", "content": response}
            ])
            
            chat_history = json.dumps(history_data)
            
        except Exception as e:
            print(f"❌ Error in conversation step {i}: {str(e)}")

def test_multilingual_support():
    """Test multilingual support."""
    
    print("\n🌍 Testing Multilingual Support")
    print("=" * 60)
    
    ollama_tools = OllamaDevstralDirectTools(enable_translation=True)
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    
    multilingual_questions = [
        ("English", "do i need to irrigate today?"),
        ("French", "combien d'eau j'ai besoin pour l'irrigation aujourd'hui?"),
        ("Spanish", "¿necesito regar hoy?"),
        ("Mixed", "show me mes parcelles irrigation status")
    ]
    
    for language, question in multilingual_questions:
        print(f"\n🌐 Testing {language}: {question}")
        
        try:
            response = ollama_tools.ask(
                question=question,
                user_id=user_id
            )
            
            print(f"✅ Response: {response}")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Ollama Devstral Direct Tests")
    print("=" * 60)
    
    # Run all tests
    test_ollama_devstral_with_real_data()
    test_conversation_flow()
    test_multilingual_support()
    
    print("\n🏁 All tests completed!")
