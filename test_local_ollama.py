#!/usr/bin/env python3
"""
Test script for the local Ollama Mistral implementation.
This script demonstrates how to use the new local.py file with Ollama.
"""

import os
import sys

# Add the magonia directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'magonia'))

from magonia.local import ask_ollama_mistral, OllamaMistralTools

def test_ollama_connection():
    """Test if Ollama is running and accessible."""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print("✅ Ollama is running!")
            print(f"Available models: {[model['name'] for model in models]}")
            return True
        else:
            print("❌ Ollama is not responding correctly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        print("Make sure <PERSON><PERSON><PERSON> is running with: ollama serve")
        return False

def test_mistral_model():
    """Test if Mistral model is available."""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            model_names = [model['name'] for model in models]
            
            if any('mistral' in name.lower() for name in model_names):
                print("✅ Mistral model is available!")
                return True
            else:
                print("❌ Mistral model not found")
                print("Available models:", model_names)
                print("To install Mistral, run: ollama pull mistral")
                return False
    except Exception as e:
        print(f"❌ Error checking Mistral model: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Testing Ollama Mistral Integration")
    print("=" * 50)
    
    # Test Ollama connection
    print("\n1. Testing Ollama connection...")
    if not test_ollama_connection():
        return
    
    # Test Mistral model
    print("\n2. Testing Mistral model availability...")
    if not test_mistral_model():
        return
    
    # Test the local implementation
    print("\n3. Testing local Ollama Mistral implementation...")
    try:
        # Initialize the tools
        tools = OllamaMistralTools()
        print(f"✅ Initialized with {len(tools.tools)} tools")
        
        # Test a simple question
        print("\n4. Testing with a simple agricultural question...")
        test_question = "What fields do I have?"
        print(f"Question: {test_question}")
        
        response = ask_ollama_mistral(test_question, user_id="test_user")
        print(f"Response: {response}")
        
        print("\n✅ Local Ollama Mistral implementation is working!")
        
    except Exception as e:
        print(f"❌ Error testing local implementation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
