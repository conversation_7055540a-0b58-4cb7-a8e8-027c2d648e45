#!/usr/bin/env python3
"""
Test script for the fixed check_highest_irrigation_requirement tool with GPT-4o
Testing the specific question: "affiche moi le nom du champ qui a besoin de plus d irrigation ?"
"""

import sys
import os
import time
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from magonia.gpt4o_direct import GPT4oDirectTools
    print("✅ Successfully imported GPT4oDirectTools")
except ImportError as e:
    print(f"❌ Failed to import GPT4oDirectTools: {e}")
    sys.exit(1)

def test_highest_irrigation_tool():
    """Test the specific question about highest irrigation requirement"""
    
    print("🚀 Testing Fixed check_highest_irrigation_requirement Tool")
    print("=" * 70)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Target Question: 'affiche moi le nom du champ qui a besoin de plus d irrigation ?'")
    print("=" * 70)
    
    # Test data
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    
    # Test questions about highest irrigation requirement
    test_questions = [
        "affiche moi le nom du champ qui a besoin de plus d irrigation ?",
        "which field needs the most irrigation?",
        "what field has the highest irrigation requirement?",
        "quel champ a le plus grand besoin d'irrigation?",
        "show me the field with highest water needs",
        "which field should I prioritize for irrigation?"
    ]
    
    print(f"👤 User ID: {user_id}")
    print(f"🧪 Total questions to test: {len(test_questions)}")
    print("=" * 70)
    
    # Initialize the agent with performance optimizations
    print("🔧 Initializing GPT-4o Agent with performance optimizations...")
    agent = GPT4oDirectTools(
        enable_translation=False,      # Fast mode for testing
        enable_context_analysis=False  # Fast mode for testing
    )
    print("✅ Agent initialized successfully")
    
    results = []
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 Test {i}/{len(test_questions)}")
        print("-" * 50)
        print(f"QUESTION: {question}")
        
        try:
            start_time = time.time()
            print(f"🔄 Processing with GPT-4o (Fast Mode)...")
            
            # Call the GPT-4o agent
            response = agent.ask(
                question=question,
                chat_history="",
                user_id=user_id
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"✅ Response received")
            print(f"📝 Response: {response}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            # Check if the response contains specific irrigation volume
            contains_volume = any(keyword in response.lower() for keyword in ['mm', 'volume', 'irrigation', 'bir ali'])
            contains_field_name = 'bir ali' in response.lower() or 'field' in response.lower()
            
            print(f"🔍 Analysis:")
            print(f"   Contains volume info: {'✅' if contains_volume else '❌'}")
            print(f"   Contains field name: {'✅' if contains_field_name else '❌'}")
            
            results.append({
                'question': question,
                'response': response,
                'execution_time': execution_time,
                'contains_volume': contains_volume,
                'contains_field_name': contains_field_name,
                'status': 'success'
            })
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            print(f"🔧 Error type: {type(e).__name__}")
            
            results.append({
                'question': question,
                'response': f"Error: {str(e)}",
                'execution_time': 0,
                'contains_volume': False,
                'contains_field_name': False,
                'status': 'error'
            })
        
        print("-" * 50)
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    successful_tests = [r for r in results if r['status'] == 'success']
    failed_tests = [r for r in results if r['status'] == 'error']
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
        print(f"⏱️  Average execution time: {avg_time:.2f} seconds")
        
        # Check tool fix effectiveness
        volume_responses = [r for r in successful_tests if r['contains_volume']]
        field_responses = [r for r in successful_tests if r['contains_field_name']]
        
        print(f"📊 Tool Fix Analysis:")
        print(f"   Responses with volume info: {len(volume_responses)}/{len(successful_tests)}")
        print(f"   Responses with field names: {len(field_responses)}/{len(successful_tests)}")
    
    if failed_tests:
        print("\n🔧 Failed Tests:")
        for test in failed_tests:
            print(f"   • {test['question']}: {test['response']}")
    
    # Focus on the main question
    print("\n🎯 Main Question Analysis:")
    target_question = "affiche moi le nom du champ qui a besoin de plus d irrigation ?"
    target_result = next((r for r in results if r['question'] == target_question), None)
    
    if target_result:
        if target_result['status'] == 'success':
            print(f"✅ Main question answered successfully!")
            print(f"📝 Answer: {target_result['response']}")
            print(f"⏱️  Time: {target_result['execution_time']:.2f} seconds")
            print(f"🔍 Contains volume: {'✅' if target_result['contains_volume'] else '❌'}")
            print(f"🔍 Contains field: {'✅' if target_result['contains_field_name'] else '❌'}")
            
            # Check for the specific fix
            if 'bir ali' in target_result['response'].lower() and any(num in target_result['response'] for num in ['7.908', '7,908']):
                print("🎉 TOOL FIX VERIFIED: Response contains specific field name and irrigation volume!")
            elif 'unknown' in target_result['response'].lower() or 'inconnu' in target_result['response'].lower():
                print("⚠️  TOOL FIX NEEDED: Response still shows 'Unknown' values")
            else:
                print("🔍 TOOL FIX STATUS: Partial - contains some data but needs verification")
        else:
            print(f"❌ Main question failed: {target_result['response']}")
    
    return results

def test_tool_directly():
    """Test the tool directly to verify the fix"""
    
    print("\n" + "=" * 70)
    print("🔧 DIRECT TOOL TEST")
    print("=" * 70)
    
    try:
        from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
        
        print("🔄 Testing check_highest_irrigation_requirement tool directly...")
        
        # Mock the Flask g object for testing
        class MockSeabexAPI:
            def tools(self):
                return self
            
            def irrigations(self):
                return self
            
            def call_tool(self, tool_name):
                # Return the actual API response format that was causing issues
                return {
                    'data': {
                        'field_name': 'BIR ali 1',
                        'highest_irrigation_volume': '7.908'
                    }
                }
        
        class MockG:
            def __init__(self):
                self.seabex_api = MockSeabexAPI()
        
        # Temporarily replace Flask's g
        import magonia.tools.check_highest_irrigation_requirement as tool_module
        original_g = getattr(tool_module, 'g', None)
        tool_module.g = MockG()
        
        # Test the tool
        result = check_highest_irrigation_requirement()
        
        print(f"✅ Tool result: {result}")
        
        # Check if the fix worked
        if '7.908' in result and 'BIR ali 1' in result:
            print("🎉 TOOL FIX VERIFIED: Tool correctly extracts irrigation volume!")
        elif 'Unknown' in result:
            print("❌ TOOL FIX FAILED: Tool still shows 'Unknown' values")
        else:
            print("🔍 TOOL FIX STATUS: Partial success")
        
        # Restore original g
        if original_g:
            tool_module.g = original_g
        
    except Exception as e:
        print(f"❌ Direct tool test failed: {e}")

if __name__ == "__main__":
    try:
        # Test the tool directly first
        test_tool_directly()
        
        # Then test through the agent
        results = test_highest_irrigation_tool()
        
        print(f"\n🏁 All tests completed!")
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
