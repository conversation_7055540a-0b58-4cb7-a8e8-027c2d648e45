# Local Ollama Mistral Implementation

This document explains how to use the new `local.py` file that integrates Ollama's Mistral model with all the agricultural tools from the original `gpt4o_direct.py` implementation.

## Prerequisites

### 1. Install Ollama

First, install Ollama on your system:

**Windows:**
```bash
# Download and install from https://ollama.ai/download
# Or use winget
winget install Ollama.Ollama
```

**macOS:**
```bash
# Download and install from https://ollama.ai/download
# Or use Homebrew
brew install ollama
```

**Linux:**
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### 2. Start Ollama Service

```bash
ollama serve
```

### 3. Install Mistral Model

```bash
ollama pull mistral
```

You can also use other models like:
- `ollama pull mistral:7b`
- `ollama pull mistral:latest`
- `ollama pull codellama` (for code-focused tasks)

## Configuration

### Environment Variables

You can configure the Ollama connection using environment variables:

```bash
# Optional: Set custom Ollama URL (default: http://localhost:11434)
export OLLAMA_BASE_URL="http://localhost:11434"

# Optional: Set custom model (default: mistral)
export OLLAMA_MODEL="mistral:7b"
```

## Usage

### Basic Usage

```python
from magonia.local import ask_ollama_mistral

# Simple question
response = ask_ollama_mistral("What fields do I have?", user_id="user123")
print(response)

# Question with chat history
chat_history = "User: Hello\nAssistant: Hi! I'm Magonia, your agricultural assistant."
response = ask_ollama_mistral("How much irrigation does CHlewi need today?", 
                             chat_history=chat_history, 
                             user_id="user123")
print(response)
```

### Advanced Usage

```python
from magonia.local import OllamaMistralTools

# Initialize with custom settings
tools = OllamaMistralTools(
    enable_translation=True,      # Enable multi-language support
    enable_context_analysis=True  # Enable conversation context analysis
)

# Ask a question
response = tools.ask(
    question="Combien d'eau faut-il pour CHlewi aujourd'hui?",  # French question
    chat_history="",
    user_id="user123"
)
print(response)  # Response will be in French
```

## Features

### 1. All Original Tools

The local implementation includes all the same tools as `gpt4o_direct.py`:

- **Time Tools**: `get_current_time_date`, `get_tomorrow`, `get_next_week`, `get_next_month`
- **Irrigation Tools**: `check_today_active_irrigation_user`, `check_irrigation_user_data`, etc.
- **Field Analysis Tools**: `get_lowest_soil_water_volume`, `fields_with_highest_evapotranspiration_next_x_days`, etc.
- **Memory Tools**: `add_memory`, `get_memories` (blocked by default for fresh data)
- **Document Lookup**: `lookup_document_tool` for RAG functionality

### 2. Multi-language Support

The system automatically detects and responds in:
- English
- French
- Spanish
- Tunisian Arabic

### 3. Same Conversation Style

- Warm, friendly, conversational tone
- Agricultural expertise focus
- Complete field data presentation
- Natural conversation flow

## Testing

Run the test script to verify everything is working:

```bash
python test_local_ollama.py
```

This will:
1. Check if Ollama is running
2. Verify Mistral model is available
3. Test the local implementation
4. Run a sample agricultural question

## Differences from GPT-4o Implementation

### Similarities
- ✅ Same system prompt and behavior
- ✅ All tools included with same functionality
- ✅ Multi-language support
- ✅ Same conversation style
- ✅ Tool execution logic identical

### Differences
- 🔄 Uses Ollama API instead of OpenAI API
- 🔄 Local model execution (no API costs)
- 🔄 Simplified tool calling (pattern-based detection)
- 🔄 May have different response quality depending on model

## Troubleshooting

### Common Issues

1. **"Cannot connect to Ollama"**
   ```bash
   # Make sure Ollama is running
   ollama serve
   ```

2. **"Mistral model not found"**
   ```bash
   # Install the model
   ollama pull mistral
   ```

3. **Slow responses**
   - Ollama runs locally and may be slower than cloud APIs
   - Consider using a smaller model or upgrading hardware

4. **Tool calling not working**
   - The implementation uses pattern-based tool detection
   - May need adjustment based on how Mistral formats responses

### Performance Tips

1. **Use appropriate model size**:
   - `mistral:7b` for better performance
   - `mistral:latest` for better quality

2. **Adjust temperature**:
   ```python
   # Lower temperature for more consistent responses
   tools = OllamaMistralTools()
   # Temperature is set to 0.7 by default in the ask method
   ```

3. **Disable features for speed**:
   ```python
   tools = OllamaMistralTools(
       enable_translation=False,      # Disable translation
       enable_context_analysis=False  # Disable context analysis
   )
   ```

## Integration with Streamlit

To use with Streamlit, modify your existing Streamlit app:

```python
# Replace the GPT-4o import
# from magonia.gpt4o_direct import ask_gpt4o_direct
from magonia.local import ask_ollama_mistral

# Use the same interface
response = ask_ollama_mistral(user_question, chat_history, user_id)
```

The interface is identical, so it's a drop-in replacement!
