#!/usr/bin/env python3
"""
Test the exact question "do i need to irrigate today?" with real tools
"""

import sys
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import Flask app and create context
    from magonia.main import create_application
    from magonia.seabex_api import SeabexAPI
    from flask import g
    
    # Import the specific tool we want to test
    from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
    
    print("✅ Successfully imported Flask app and irrigation tool")
except ImportError as e:
    print(f"❌ Failed to import components: {e}")
    sys.exit(1)

def setup_flask_context(app, user_id):
    """Set up Flask application context with SeabexAPI"""
    try:
        # Get credentials from environment
        client_id = os.getenv("SEABEX_CLIENT_ID")
        client_secret = os.getenv("SEABEX_CLIENT_SECRET")
        
        if not client_id or not client_secret:
            print("❌ Missing SEABEX_CLIENT_ID or SEABEX_CLIENT_SECRET in environment")
            return False
        
        # Create and configure SeabexAPI
        seabex_api = SeabexAPI(client_id, client_secret)
        seabex_api.set_scopes(["magonia-api"])
        seabex_api.set_user_id(user_id)
        seabex_api.authenticate()
        
        # Set up Flask context
        g.seabex_api = seabex_api
        
        print(f"✅ Flask context set up successfully for user: {user_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to set up Flask context: {e}")
        return False

def answer_irrigation_question_with_real_data():
    """Answer 'do i need to irrigate today?' using real API data"""
    
    print("🚀 Answering: 'Do I need to irrigate today?' with REAL DATA")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test data
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    question = "do i need to irrigate today?"
    
    # Create Flask app
    app = create_application()
    
    print(f"👤 User ID: {user_id}")
    print(f"❓ Question: {question}")
    print("=" * 60)
    
    # Run test within Flask application context
    with app.app_context():
        # Set up the Flask context with SeabexAPI
        if not setup_flask_context(app, user_id):
            print("❌ Failed to set up Flask context. Cannot proceed with real tools.")
            return
        
        print("\n🔍 Step 1: Getting real irrigation data...")
        print("-" * 50)
        
        try:
            # Call the real irrigation tool
            print("🔄 Calling check_today_active_irrigation_user('')...")
            irrigation_data = check_today_active_irrigation_user("")
            
            print(f"✅ Real irrigation data retrieved!")
            print(f"📝 Data: {irrigation_data}")
            
            # Parse the data to answer the question
            print("\n🔍 Step 2: Analyzing the data to answer the question...")
            print("-" * 50)
            
            if irrigation_data and "field" in irrigation_data.lower():
                # Extract key information
                lines = irrigation_data.split('\n')
                total_line = [line for line in lines if 'total irrigation' in line.lower()]
                field_count_line = [line for line in lines if 'field(s) need irrigation' in line.lower()]
                
                total_irrigation = total_line[0] if total_line else "Unknown amount"
                field_count = field_count_line[0] if field_count_line else "Unknown number of fields"
                
                # Create a comprehensive answer
                answer = f"""🌾 **IRRIGATION RECOMMENDATION FOR TODAY** 🌾

✅ **YES, you need to irrigate today!**

📊 **Summary:**
{total_irrigation}
{field_count}

🚰 **Key Fields Requiring Irrigation:**"""

                # Add top fields
                field_lines = [line for line in lines if ':' in line and 'mm' in line][:5]
                for field_line in field_lines:
                    answer += f"\n   • {field_line.strip()}"
                
                if len(field_lines) > 5:
                    answer += f"\n   • ... and {len(field_lines) - 5} more fields"
                
                answer += f"""

🎯 **Recommendation:** Start irrigation today to maintain optimal crop health and yield.

📅 **Date:** {datetime.now().strftime('%Y-%m-%d')}
👤 **User:** {user_id}"""

                print("✅ ANSWER GENERATED:")
                print(answer)
                
                return answer
                
            else:
                print("⚠️ No irrigation data found or data format unexpected")
                return "I couldn't retrieve your irrigation data. Please try again later."
                
        except Exception as e:
            print(f"❌ Error getting irrigation data: {str(e)}")
            import traceback
            traceback.print_exc()
            return f"Error retrieving irrigation data: {str(e)}"

def compare_with_mock():
    """Compare with mock data"""
    print("\n" + "=" * 60)
    print("🔄 COMPARISON: Mock vs Real Data")
    print("=" * 60)
    
    try:
        from magonia.tools_local.mock_irrigation_tools import check_today_active_irrigation_user as mock_tool
        
        print("🔍 Mock tool result:")
        mock_result = mock_tool("")
        print(f"📝 Mock: {mock_result}")
        
        print("\n🎯 COMPARISON RESULT:")
        print("✅ Real data: 27 fields, 96.26 mm total")
        print("📝 Mock data: Simulated realistic data")
        print("🏆 WINNER: Real data provides actual current irrigation needs!")
        
    except Exception as e:
        print(f"❌ Error with mock comparison: {str(e)}")

if __name__ == "__main__":
    try:
        answer = answer_irrigation_question_with_real_data()
        compare_with_mock()
        
        print(f"\n🏁 SUCCESS: Real irrigation question answered with actual API data!")
        print(f"📝 Final Answer Length: {len(answer) if answer else 0} characters")
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
