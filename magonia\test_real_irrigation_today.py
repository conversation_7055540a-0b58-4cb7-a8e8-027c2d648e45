#!/usr/bin/env python3
"""
Test script for real irrigation tools with Devstral
Testing the specific question: "do i need to irrigate today?"
"""

import sys
import os
import time
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from magonia.local import ask_ollama_devstral
    print("✅ Successfully imported ask_ollama_devstral from local.py")
except ImportError as e:
    print(f"❌ Failed to import ask_ollama_devstral: {e}")
    sys.exit(1)

def test_irrigation_today():
    """Test the specific question about irrigation needs today"""
    
    print("🚀 Testing Real Irrigation Tools with Devstral")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Target Question: 'do i need to irrigate today?'")
    print("=" * 60)
    
    # Test data
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    session_id = "test_session_real_tools"
    
    # Test questions about irrigation today
    test_questions = [
        "do i need to irrigate today?",
        "est-ce que j'ai besoin d'irriguer aujourd'hui?",
        "what fields need irrigation today?",
        "quels champs ont besoin d'irrigation aujourd'hui?",
        "show me today's irrigation status",
        "affiche moi le statut d'irrigation d'aujourd'hui"
    ]
    
    print(f"👤 User ID: {user_id}")
    print(f"📝 Session ID: {session_id}")
    print(f"🧪 Total questions to test: {len(test_questions)}")
    print("=" * 60)
    
    results = []
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 Test {i}/{len(test_questions)}")
        print("-" * 40)
        print(f"QUESTION: {question}")
        
        try:
            start_time = time.time()
            print(f"🔄 Processing with Devstral (Real Tools)...")
            
            # Call the real Devstral function with real tools
            response = ask_ollama_devstral(
                question=question,
                chat_history="",
                user_id=user_id
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"✅ Response received")
            print(f"📝 Response: {response}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            results.append({
                'question': question,
                'response': response,
                'execution_time': execution_time,
                'status': 'success'
            })
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            print(f"🔧 Error type: {type(e).__name__}")
            
            results.append({
                'question': question,
                'response': f"Error: {str(e)}",
                'execution_time': 0,
                'status': 'error'
            })
        
        print("-" * 40)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['status'] == 'success']
    failed_tests = [r for r in results if r['status'] == 'error']
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
        print(f"⏱️  Average execution time: {avg_time:.2f} seconds")
    
    if failed_tests:
        print("\n🔧 Failed Tests:")
        for test in failed_tests:
            print(f"   • {test['question']}: {test['response']}")
    
    print("\n🎯 Key Question Analysis:")
    target_question = "do i need to irrigate today?"
    target_result = next((r for r in results if r['question'] == target_question), None)
    
    if target_result:
        if target_result['status'] == 'success':
            print(f"✅ Target question answered successfully!")
            print(f"📝 Answer: {target_result['response'][:200]}...")
            print(f"⏱️  Time: {target_result['execution_time']:.2f} seconds")
        else:
            print(f"❌ Target question failed: {target_result['response']}")
    
    return results

if __name__ == "__main__":
    try:
        results = test_irrigation_today()
        print(f"\n🏁 Test completed. Check results above.")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
