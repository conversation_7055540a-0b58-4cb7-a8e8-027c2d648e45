#!/usr/bin/env python3
"""
Test script for real irrigation tools with Devstral using Flask API
Similar to send_test.py but focused on irrigation questions
"""

import os
import time
import traceback
import requests
import json
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get authentication token
token = os.getenv('AUTH_TOKEN')

def send_irrigation_prompt(prompt, session_id="irrigation_test_session", user_id="f68381cd-a748-47bd-842c-701790b35e3c", token=None):
    """
    Send irrigation prompt to Flask API endpoint (similar to send_test.py)
    This ensures proper Flask context for tool execution
    """
    url = "http://127.0.0.1:8080/magonia/chat"
    data = {
        "session_id": session_id,
        "prompt": prompt,
        "chat_history": "",
        "scopes": ["magonia-api"],
        "user_id": user_id
    }
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }

    try:
        response = requests.post(url, headers=headers, data=json.dumps(data), timeout=120)
        print(f"📡 Status Code: {response.status_code}")

        if response.status_code != 200:
            print(f"❌ Error Response: {response.text}")
            return {
                'error': f'HTTP {response.status_code}',
                'response': response.text,
                'status': 'failed'
            }

        try:
            json_response = response.json()
            return {
                'response': json_response,
                'status': 'success'
            }
        except Exception as e:
            print(f"❌ JSON parsing error: {e}")
            return {
                'error': 'Invalid JSON response',
                'response': response.text,
                'status': 'failed'
            }

    except requests.exceptions.Timeout:
        return {
            'error': 'Request timeout (120s)',
            'response': 'The request took too long to complete',
            'status': 'timeout'
        }
    except Exception as e:
        print(f"❌ Request error: {e}")
        traceback.print_exc()
        return {
            'error': str(e),
            'response': 'Request failed',
            'status': 'failed'
        }

def test_irrigation_today_with_devstral():
    """
    Test irrigation questions using Flask API with Devstral
    This ensures proper Flask context for tool execution
    """

    print("🚀 Testing Real Irrigation Tools with Devstral via Flask API")
    print("=" * 70)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Using Flask API endpoint (like send_test.py)")
    print(f"🤖 Model: Devstral with real irrigation tools")
    print("=" * 70)

    # Check if token is available
    if not token:
        print("❌ AUTH_TOKEN not found in environment variables")
        print("💡 Please set AUTH_TOKEN in your .env file")
        return []

    # Test data
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    session_id = "devstral_irrigation_test"

    # Irrigation questions to test (your specific requests)
    test_questions = [
        #"do i need to irrigate today?",
        #"what fields need irrigation today?",
        #"affiche moi le nom du champ qui a besoin de plus d irrigation ?",
        #"est-ce que j'ai besoin d'irriguer aujourd'hui?",
        #"quels champs ont besoin d'irrigation aujourd'hui?",
        #"show me all my parcels irrigation status",
        #"what is my current irrigation status?",
        #"which field needs the most irrigation?",
        #"how much water do I need for irrigation today?",
        #"combien d'eau j'ai besoin pour l'irrigation aujourd'hui?"
        #"quelles sont mes parcelles ?",
        "يجب أن أسقي اليوم"
        #cd magonia && python test_real_irrigation_today.py
    ]

    print(f"👤 User ID: {user_id}")
    print(f"📝 Session ID: {session_id}")
    print(f"🔑 Token: {'✅ Available' if token else '❌ Missing'}")
    print(f"🧪 Total questions to test: {len(test_questions)}")
    print("=" * 70)

    results = []

    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 Test {i}/{len(test_questions)}")
        print("-" * 50)
        print(f"❓ QUESTION: {question}")

        start_time = time.time()
        print(f"🔄 Sending to Flask API with Devstral...")

        # Send request via Flask API (ensures proper context)
        result = send_irrigation_prompt(
            prompt=question,
            session_id=session_id,
            user_id=user_id,
            token=token
        )

        end_time = time.time()
        execution_time = end_time - start_time

        if result['status'] == 'success':
            response_text = result['response']
            print(f"✅ Response received successfully")
            print(f"📝 Response: {response_text}")

            # Analyze response content
            response_str = str(response_text).lower()
            has_field_info = any(keyword in response_str for keyword in ['field', 'champ', 'parcelle', 'parcel'])
            has_irrigation_data = any(keyword in response_str for keyword in ['irrigation', 'irriguer', 'water', 'eau', 'mm'])
            has_today_info = any(keyword in response_str for keyword in ['today', "aujourd'hui", 'date'])

            print(f"🔍 Analysis:")
            print(f"   Contains field info: {'✅' if has_field_info else '❌'}")
            print(f"   Contains irrigation data: {'✅' if has_irrigation_data else '❌'}")
            print(f"   Contains today info: {'✅' if has_today_info else '❌'}")

        else:
            print(f"❌ Request failed: {result['status']}")
            print(f"🔧 Error: {result.get('error', 'Unknown error')}")
            response_text = result.get('response', 'No response')

        print(f"⏱️  Execution time: {execution_time:.2f} seconds")

        results.append({
            'question': question,
            'response': response_text,
            'execution_time': execution_time,
            'status': result['status']
        })

        print("-" * 50)

        # Small delay between requests
        time.sleep(1)

    # Summary
    print("\n" + "=" * 70)
    print("📊 DEVSTRAL IRRIGATION TEST SUMMARY")
    print("=" * 70)

    successful_tests = [r for r in results if r['status'] == 'success']
    failed_tests = [r for r in results if r['status'] != 'success']
    timeout_tests = [r for r in results if r['status'] == 'timeout']

    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}/{len(results)}")
    print(f"⏰ Timeout tests: {len(timeout_tests)}/{len(results)}")

    if successful_tests:
        avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
        min_time = min(r['execution_time'] for r in successful_tests)
        max_time = max(r['execution_time'] for r in successful_tests)
        print(f"⏱️  Average execution time: {avg_time:.2f} seconds")
        print(f"⚡ Fastest response: {min_time:.2f} seconds")
        print(f"🐌 Slowest response: {max_time:.2f} seconds")

    if failed_tests:
        print(f"\n🔧 Failed Tests ({len(failed_tests)}):")
        for test in failed_tests:
            print(f"   • {test['question'][:50]}{'...' if len(test['question']) > 50 else ''}")
            print(f"     Status: {test['status']}")

    # Key question analysis
    print(f"\n🎯 KEY IRRIGATION QUESTIONS ANALYSIS:")
    key_questions = [
        "do i need to irrigate today?",
        "what fields need irrigation today?",
        "affiche moi le nom du champ qui a besoin de plus d irrigation ?"
    ]

    for question in key_questions:
        result = next((r for r in results if r['question'] == question), None)
        if result:
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"{status_icon} {question}")
            if result['status'] == 'success':
                print(f"   ⏱️  Time: {result['execution_time']:.2f}s")
                response_preview = str(result['response'])[:100].replace('\n', ' ')
                print(f"   📝 Preview: {response_preview}...")
            else:
                print(f"   🔧 Issue: {result['status']}")

    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    if successful_tests:
        print("   ✅ Devstral is working with Flask API!")
        print("   ✅ Real irrigation tools are accessible via API")
        print("   ✅ Tool execution context is properly handled")

        if len(successful_tests) == len(results):
            print("   🎉 All tests passed! System is fully functional")
        else:
            print(f"   ⚠️  {len(failed_tests)} tests failed - check specific errors")
    else:
        print("   ❌ No successful tests - check Flask server and authentication")
        print("   🔧 Verify Flask server is running on http://127.0.0.1:8080")
        print("   🔑 Verify AUTH_TOKEN is set correctly")

    return results

if __name__ == "__main__":
    try:
        print("🌾 DEVSTRAL IRRIGATION TOOL EXECUTION TEST")
        print("🎯 Testing real irrigation data with Devstral via Flask API")
        print("📡 This test uses the same approach as send_test.py")
        print()

        results = test_irrigation_today_with_devstral()

        if results:
            successful_count = len([r for r in results if r['status'] == 'success'])
            print(f"\n🏁 Test completed: {successful_count}/{len(results)} successful")
        else:
            print(f"\n🏁 Test completed with no results")

    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
