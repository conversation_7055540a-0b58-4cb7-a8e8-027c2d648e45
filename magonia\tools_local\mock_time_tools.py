"""
Mock time tools for local Ollama testing.
"""

from typing import Optional
from langchain_core.tools import tool
from datetime import datetime, timedelta

@tool
def get_current_time_date(tool_input: Optional[str] = None) -> str:
    """
    Get the current date and time.
    
    Returns:
        str: Current date and time
    """
    try:
        now = datetime.now()
        return f"The current date and time is {now.strftime('%Y-%m-%d %H:%M:%S')}."
    except Exception as e:
        return f"Error getting current time: {str(e)}"

@tool
def get_tomorrow(tool_input: Optional[str] = None) -> str:
    """
    Get tomorrow's date.
    
    Returns:
        str: Tomorrow's date
    """
    try:
        tomorrow = datetime.now() + timedelta(days=1)
        return f"Tomorrow's date is {tomorrow.strftime('%Y-%m-%d')}."
    except Exception as e:
        return f"Error getting tomorrow's date: {str(e)}"

@tool
def get_next_week(tool_input: Optional[str] = None) -> str:
    """
    Get next week's date.
    
    Returns:
        str: Next week's date
    """
    try:
        next_week = datetime.now() + timedelta(weeks=1)
        return f"Next week's date is {next_week.strftime('%Y-%m-%d')}."
    except Exception as e:
        return f"Error getting next week's date: {str(e)}"

@tool
def get_next_month(tool_input: Optional[str] = None) -> str:
    """
    Get next month's date.
    
    Returns:
        str: Next month's date
    """
    try:
        now = datetime.now()
        if now.month == 12:
            next_month = now.replace(year=now.year + 1, month=1)
        else:
            next_month = now.replace(month=now.month + 1)
        return f"Next month's date is {next_month.strftime('%Y-%m-%d')}."
    except Exception as e:
        return f"Error getting next month's date: {str(e)}"
