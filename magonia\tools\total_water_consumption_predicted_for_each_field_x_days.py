from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import parse_integer_from_string

@tool
def total_water_consumption_predicted_for_each_field_x_days(days: Optional[int] = 0) -> dict:
    """
    Calculate the total water consumption predicted for each field over the next X days.

    Args:
        days (int, optional): The number of future days to check for water consumption. 
                    Default is 0 (today).

    Returns:
        dict: The total water consumption predicted for each field or an error message.
    """
    try:
        # Only parse if days is a string
        if isinstance(days, str):
            days = parse_integer_from_string(days)
        
        # Ensure days is an integer and non-negative
        if not isinstance(days, int) or days < 0:
            return {"message": "Please provide a valid number of days (0 or positive number) to check water consumption predictions."}

        response = g.seabex_api.tools().irrigations().call_tool(
            'total_water_consumption_predicted_for_each_field_x_days',
            {'days': days}
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"I encountered an issue while retrieving the data: {error_message}. Please try again in a moment."}

        # Handle empty or no data response
        if not response or (isinstance(response, dict) and not response.get('data')):
            return {
                "message": (
                    "I don't have water consumption predictions available for the next {days} days. "
                    "This could be because:\n"
                    "• Your fields are in a period of low water consumption\n"
                    "• The weather conditions are favorable for water retention\n"
                    "• The system is still collecting data for this period\n\n"
                    "Would you like to:\n"
                    "• Check a different time period?\n"
                    "• Look at current soil moisture levels?\n"
                    "• Get irrigation recommendations for today?"
                ).format(days=days)
            }

        return response

    except Exception as e:
        print(f"Error calculating total water consumption: {e}")
        return {
            "message": "I'm having trouble accessing the water consumption predictions right now. This might be temporary - please try again in a few moments. If the issue persists, our support team can help investigate."
        }
