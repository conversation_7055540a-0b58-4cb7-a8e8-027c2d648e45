import re
from datetime import datetime
from typing import Optional
from flask import g
from langchain_core.tools import tool

from utils.utils import extract_value

@tool
def check_soil_water_volume(
    field_name: Optional[str] = None,
    calculation_date: Optional[str] = None
) -> str:
    """
    Retrieve the soil water volume for a specific field on a given date.

    Args:
        field_name (str): The name of the field. This is a required argument.
        calculation_date (str): The date of calculation in 'YYYY-MM-DD' format. This is a required argument.

    Returns:
        str: Soil water volume information or a prompt requesting the missing information.
    """
    try:
        # Handle missing field_name
        if not field_name:
            return "To help you check the soil water volume, I'll need the name of the field you're interested in. Could you please provide that?"

        # If field_name contains multiple parts, extract date if available
        if ',' in field_name:
            parts = field_name.split(',')
            field_name = parts[0].strip()  # First part as field name
            if len(parts) > 1:
                calculation_date = parts[1].strip()  # Second part as calculation_date

        # Clean field_name input
        field_name = extract_value(field_name, "field_name")

        # Handle missing calculation_date
        if not calculation_date:
            return f"I'd be happy to check the soil water volume for '{field_name}'. Could you please specify which date you'd like to check?"

        # Clean and format the calculation_date
        if isinstance(calculation_date, str):
            calculation_date = extract_value(calculation_date, "calculation_date")
        if isinstance(calculation_date, datetime):
            calculation_date = calculation_date.strftime('%Y-%m-%d')

        calculation_date = calculation_date or datetime.now().strftime('%Y-%m-%d')

        response = g.seabex_api.tools().soils().call_tool(
            'check_soil_water_volume',
            {
                'field_name': field_name,
                'calculation_date': calculation_date
             }
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"I encountered an issue while retrieving the data: {error_message}. Please try again in a moment."

        if response is not None:
            return (
                f"The soil water volume for '{field_name}' on {calculation_date} is {float(response):.2f} mm. "
                f"This measurement helps us understand how well your field is retaining water."
            )
        else:
            return (
                f"I couldn't find soil water volume data for '{field_name}' on {calculation_date}. "
                f"This could be because:\n"
                f"• The field wasn't monitored on this date\n"
                f"• The date is outside our monitoring period\n"
                f"• The field name might need to be verified\n\n"
                f"Would you like to try a different date or verify the field name?"
            )

    except Exception as e:
        print(e)
        return (
            "I'm having trouble accessing the soil water volume data right now. "
            "This might be temporary - please try again in a few moments. "
            "If the issue persists, our support team can help investigate."
        )

