#!/usr/bin/env python3
"""
Comprehensive test script for real irrigation data with Ollama model comparison
Tests both Devstral and Mistral models on today's irrigation questions with real API data
"""

import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, List, Any

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_with_model(model_name: str, questions: List[str], user_id: str) -> List[Dict]:
    """Test irrigation questions with a specific Ollama model"""
    
    print(f"\n🤖 Testing with {model_name.upper()}")
    print("=" * 60)
    
    # Set the model environment variable
    os.environ["OLLAMA_MODEL"] = model_name
    
    try:
        # Import after setting environment variable
        from magonia.local import ask_ollama_devstral
        print(f"✅ Successfully imported ask_ollama_devstral for {model_name}")
    except ImportError as e:
        print(f"❌ Failed to import for {model_name}: {e}")
        return []
    
    results = []
    
    for i, question in enumerate(questions, 1):
        print(f"\n🔍 Test {i}/{len(questions)} with {model_name}")
        print("-" * 50)
        print(f"QUESTION: {question}")
        
        try:
            start_time = time.time()
            print(f"🔄 Processing with {model_name}...")
            
            # Call the function with the current model
            response = ask_ollama_devstral(
                question=question,
                chat_history="",
                user_id=user_id
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"✅ Response received")
            print(f"📝 Response: {response[:200]}{'...' if len(response) > 200 else ''}")
            print(f"⏱️  Execution time: {execution_time:.2f} seconds")
            
            # Analyze response content
            contains_field_info = any(keyword in response.lower() for keyword in ['field', 'champ', 'bir ali', 'parcelle'])
            contains_irrigation_data = any(keyword in response.lower() for keyword in ['mm', 'irrigation', 'water', 'volume'])
            contains_today_info = any(keyword in response.lower() for keyword in ['today', "aujourd'hui", 'current'])
            
            print(f"🔍 Analysis:")
            print(f"   Contains field info: {'✅' if contains_field_info else '❌'}")
            print(f"   Contains irrigation data: {'✅' if contains_irrigation_data else '❌'}")
            print(f"   Contains today info: {'✅' if contains_today_info else '❌'}")
            
            results.append({
                'model': model_name,
                'question': question,
                'response': response,
                'execution_time': execution_time,
                'contains_field_info': contains_field_info,
                'contains_irrigation_data': contains_irrigation_data,
                'contains_today_info': contains_today_info,
                'response_length': len(response),
                'status': 'success'
            })
            
        except Exception as e:
            print(f"❌ Error with {model_name}: {str(e)}")
            print(f"🔧 Error type: {type(e).__name__}")
            
            results.append({
                'model': model_name,
                'question': question,
                'response': f"Error: {str(e)}",
                'execution_time': 0,
                'contains_field_info': False,
                'contains_irrigation_data': False,
                'contains_today_info': False,
                'response_length': 0,
                'status': 'error'
            })
        
        print("-" * 50)
    
    return results

def compare_models_performance(all_results: List[Dict]) -> None:
    """Compare performance between different models"""
    
    print("\n" + "=" * 80)
    print("📊 MODEL COMPARISON ANALYSIS")
    print("=" * 80)
    
    # Group results by model
    models = {}
    for result in all_results:
        model = result['model']
        if model not in models:
            models[model] = []
        models[model].append(result)
    
    # Compare each model
    for model_name, results in models.items():
        successful_tests = [r for r in results if r['status'] == 'success']
        failed_tests = [r for r in results if r['status'] == 'error']
        
        print(f"\n🤖 {model_name.upper()} PERFORMANCE:")
        print(f"   ✅ Successful: {len(successful_tests)}/{len(results)}")
        print(f"   ❌ Failed: {len(failed_tests)}/{len(results)}")
        
        if successful_tests:
            avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
            avg_length = sum(r['response_length'] for r in successful_tests) / len(successful_tests)
            field_info_count = sum(1 for r in successful_tests if r['contains_field_info'])
            irrigation_data_count = sum(1 for r in successful_tests if r['contains_irrigation_data'])
            today_info_count = sum(1 for r in successful_tests if r['contains_today_info'])
            
            print(f"   ⏱️  Average time: {avg_time:.2f} seconds")
            print(f"   📏 Average response length: {avg_length:.0f} characters")
            print(f"   🏞️  Field info responses: {field_info_count}/{len(successful_tests)}")
            print(f"   💧 Irrigation data responses: {irrigation_data_count}/{len(successful_tests)}")
            print(f"   📅 Today info responses: {today_info_count}/{len(successful_tests)}")
    
    # Head-to-head comparison
    if len(models) >= 2:
        model_names = list(models.keys())
        print(f"\n🥊 HEAD-TO-HEAD COMPARISON:")
        print(f"   Models: {' vs '.join(model_names)}")
        
        for question in [r['question'] for r in all_results[:len(all_results)//len(models)]]:
            print(f"\n   📋 Question: {question[:50]}...")
            for model_name in model_names:
                model_result = next((r for r in models[model_name] if r['question'] == question), None)
                if model_result and model_result['status'] == 'success':
                    print(f"      {model_name}: {model_result['execution_time']:.1f}s, {model_result['response_length']} chars")
                else:
                    print(f"      {model_name}: FAILED")

def test_real_irrigation_comparison():
    """Main test function comparing models on real irrigation data"""
    
    print("🌾 REAL IRRIGATION DATA COMPARISON TEST")
    print("=" * 80)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Testing real irrigation questions with multiple Ollama models")
    print("=" * 80)
    
    # Test configuration
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    
    # Comprehensive irrigation questions (English and French)
    test_questions = [
        # Today's irrigation needs
        "do i need to irrigate today?",
        "est-ce que j'ai besoin d'irriguer aujourd'hui?",
        
        # Field-specific questions
        "what fields need irrigation today?",
        "quels champs ont besoin d'irrigation aujourd'hui?",
        
        # Highest irrigation requirement (the fixed tool)
        "affiche moi le nom du champ qui a besoin de plus d irrigation ?",
        "which field needs the most irrigation?",
        
        # All parcels/fields information
        "show me all my parcels irrigation status",
        "affiche moi le statut d'irrigation de toutes mes parcelles",
        
        # Current irrigation status
        "what is my current irrigation status?",
        "quel est mon statut d'irrigation actuel?",
        
        # Specific volume questions
        "how much water do I need for irrigation today?",
        "combien d'eau j'ai besoin pour l'irrigation aujourd'hui?"
    ]
    
    # Models to test
    models_to_test = [
        "devstral:latest",
        "mistral:latest"
    ]
    
    print(f"👤 User ID: {user_id}")
    print(f"🧪 Questions to test: {len(test_questions)}")
    print(f"🤖 Models to test: {', '.join(models_to_test)}")
    print("=" * 80)
    
    all_results = []
    
    # Test each model
    for model in models_to_test:
        try:
            model_results = test_with_model(model, test_questions, user_id)
            all_results.extend(model_results)
        except Exception as e:
            print(f"❌ Failed to test {model}: {e}")
            continue
    
    # Compare results
    if all_results:
        compare_models_performance(all_results)
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"irrigation_comparison_results_{timestamp}.json"
        
        try:
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(all_results, f, indent=2, ensure_ascii=False)
            print(f"\n💾 Detailed results saved to: {results_file}")
        except Exception as e:
            print(f"⚠️  Could not save results file: {e}")
    
    # Focus on key questions
    print("\n🎯 KEY QUESTION ANALYSIS:")
    key_questions = [
        "do i need to irrigate today?",
        "affiche moi le nom du champ qui a besoin de plus d irrigation ?",
        "show me all my parcels irrigation status"
    ]
    
    for question in key_questions:
        print(f"\n📋 Question: {question}")
        question_results = [r for r in all_results if r['question'] == question]
        
        for result in question_results:
            if result['status'] == 'success':
                print(f"   {result['model']}: ✅ {result['execution_time']:.1f}s")
                if result['contains_irrigation_data'] and result['contains_field_info']:
                    print(f"      🎉 Contains both field and irrigation data!")
                elif result['contains_irrigation_data']:
                    print(f"      💧 Contains irrigation data")
                elif result['contains_field_info']:
                    print(f"      🏞️  Contains field info")
                else:
                    print(f"      ⚠️  Limited data content")
            else:
                print(f"   {result['model']}: ❌ FAILED")
    
    return all_results

if __name__ == "__main__":
    try:
        print("🚀 Starting Real Irrigation Data Comparison Test...")
        results = test_real_irrigation_comparison()
        
        if results:
            successful_results = [r for r in results if r['status'] == 'success']
            print(f"\n🏁 Test completed!")
            print(f"✅ Total successful tests: {len(successful_results)}/{len(results)}")
            print(f"📊 Models tested: {len(set(r['model'] for r in results))}")
            print(f"🧪 Questions tested: {len(set(r['question'] for r in results))}")
        else:
            print("\n❌ No results obtained. Check Ollama service and model availability.")
            
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
