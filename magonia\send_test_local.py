import os
import time
import traceback
from dotenv import load_dotenv

# Import the local Ollama implementation
import sys
import os

# Add parent directory to path so magonia module can be found
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from local_with_mock_tools import ask_ollama_devstral_mock

load_dotenv()

# Get credentials from environment variables (like send_test.py)
AUTH_TOKEN = os.getenv('AUTH_TOKEN')
DEFAULT_USER_ID = "f68381cd-a748-47bd-842c-701790b35e3c"  # From send_test.py
DEFAULT_SESSION_ID = "session123"  # From send_test.py

def send_prompt_local(prompt, session_id=DEFAULT_SESSION_ID, user_id=DEFAULT_USER_ID):
    """
    Send a prompt to the local Ollama Mistral implementation
    """
    try:
        print(f"🔄 Processing prompt with Ollama Devstral...")
        print(f"📝 Session ID: {session_id}")
        print(f"👤 User ID: {user_id}")

        # Call the local Ollama implementation with mock tools
        response = ask_ollama_devstral_mock(
            question=prompt,
            user_id=user_id,
            chat_history=""
        )
        
        print("✅ Status: Success")
        print("\033[33m" + "Response: " + str(response) + "\033[0m")
        return response
        
    except Exception as e:
        print("❌ Status: Error")
        print(f"Error: {str(e)}")
        traceback.print_exc()
        return {
            'error': f'An unexpected error occurred: {str(e)}',
            'status': 'error'
        }


# Comprehensive test prompts to test different tools and scenarios
prompts = [
    # === FIELD LISTING TESTS (get_all_user_areas_with_children) ===
    "quelles sont mes parcelles ?",
    "List all my fields",
    "Show me my agricultural areas",
    "What fields do I have?",

    # === IRRIGATION STATUS TESTS (get_current_irrigation_status, check_today_active_irrigation_user) ===
    "affiche moi le nom du champ qui a besoin de plus d irrigation ?",
    "Which fields need irrigation today?",
    "What is the current irrigation status?",
    "Check today's irrigation needs",
    "est ce que je dois irriguer aujourd hui ?",
    "do i have to irrigate my field Today ?",

    # === SPECIFIC FIELD TESTS (check_irrigation_user_data) ===
    "Do I have to irrigate my field CHlewi on 2025-07-01",
    "Check irrigation data for CHlewi field today",
    "What's the soil moisture for Taba1?",
    "do i have to irrigate my field Taba1 on 2025-07-01 ?",

    # === SUPPORTED CROPS TESTS (lookup_document_tool) ===
    "Quelles sont les cultures supportées par la plateforme Seabex?",
    "What crops does Seabex support?",
    "Show me supported cultures",
    "Quelles sont les céréales supportées par seabex?",

    # === TIME-RELATED TESTS (time tools) ===
    "What is the current time?",
    "what s the date of today?",
    "Quelle est la date d'aujourd'hui?",
    "What's tomorrow's date?",

    # === IRRIGATION FORECASTING TESTS (check_irrigation_need_for_x_days) ===
    "Show me irrigation needs for the next 3 days",
    "Check irrigation requirements for next week",
    "do i need to irrigate in the next days ?",

    # === AGRICULTURAL GUIDANCE TESTS (lookup_document_tool) ===
    "Comment tracer ma parcelle?",
    "What are the best irrigation practices?",
    "Comment créer un cycle de production et activer le calcul du bilan hydrique?",
    "what is the best time to irrigate my fields ?",
    "Quels sont les avantages de l'irrigation goutte-à-goutte pour la gestion de l'eau en agriculture ?",

    # === MEMORY TESTS (memory tools) ===
    "Hi! my name is kalil",
    "Remember that I prefer morning irrigation",
    "What do you remember about my preferences?",
    "what s my name again ?",

    # === MULTI-LANGUAGE TESTS ===
    "¿Cuáles son mis campos?",  # Spanish
    "¿Tengo que regar mi campo CHlewi el 1 de julio de 2025?",  # Spanish
    "وين الحقول متاعي؟",  # Tunisian Arabic
    "يلزمني نسقي الحقل متاعي ش CHlewi لوي نهار 2025-07-01؟",  # Tunisian Arabic
    "يجب أن أسقي اليوم؟",  # Arabic
    "شنوة أنواع نخيل التمر اللي يدعموهم Seabex؟",  # Tunisian Arabic

    # === ERROR HANDLING TESTS ===
    "Do I have to irrigate my field NonExistentField on 2025-07-01",
    "Check irrigation for invalid field name",

    # === NON-AGRICULTURAL TESTS (should be redirected) ===
    "comment on preparer une pizza ?",
    "what are the best places to visit around the world ?",

    # === COMPLEX QUERIES ===
    "Which field has the lowest soil moisture and needs irrigation most urgently?",
    "Can you give me a complete irrigation plan for today?",
    "What's the total area of all my fields that need irrigation?",
]


if __name__ == "__main__":
    # Start the timer
    total_start_time = time.time()
    session_id = DEFAULT_SESSION_ID
    user_id = DEFAULT_USER_ID

    print("🚀 Starting Local Ollama Mistral Test Suite")
    print("=" * 60)
    print(f"📝 Using session_id: {session_id}")
    print(f"👤 Using user_id: {user_id}")
    print(f"🔑 Auth token available: {'Yes' if AUTH_TOKEN else 'No'}")
    print(f"🧪 Total prompts to test: {len(prompts)}")
    print("=" * 60)
    print("🔧 Testing comprehensive tool coverage:")
    print("   • Field listing tools")
    print("   • Irrigation status tools")
    print("   • Specific field queries")
    print("   • Supported crops lookup")
    print("   • Time/date tools")
    print("   • Irrigation forecasting")
    print("   • Agricultural guidance")
    print("   • Memory tools")
    print("   • Multi-language support")
    print("   • Error handling")
    print("=" * 60)

    # Process each prompt
    for i, prompt in enumerate(prompts, 1):
        print(f"\n🔍 Test {i}/{len(prompts)}")
        print("-" * 40)
        
        start_time = time.time()
        print("\033[31m" + "PROMPT: " + "\033[0m" + "\033[32m" + prompt + "\033[0m")
        
        # Send prompt to local Ollama
        response = send_prompt_local(
            prompt=prompt, 
            session_id=session_id, 
            user_id=user_id
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Format execution time
        if execution_time < 1:
            time_message = f"{execution_time * 1000:.2f} ms"
        elif execution_time < 60:
            time_message = f"{execution_time:.2f} seconds"
        else:
            minutes = execution_time // 60
            seconds = execution_time % 60
            time_message = f"{int(minutes)} minute(s) and {seconds:.2f} seconds"
        
        print(f"⏱️  Execution time: {time_message}")
        print("-" * 40)

    # Calculate total execution time
    total_end_time = time.time()
    total_execution_time = total_end_time - total_start_time

    # Format total execution time
    if total_execution_time < 60:
        total_time_message = f"{total_execution_time:.2f} seconds"
    else:
        minutes = total_execution_time // 60
        seconds = total_execution_time % 60
        total_time_message = f"{int(minutes)} minute(s) and {seconds:.2f} seconds"

    print("\n" + "=" * 60)
    print("🎉 Local Ollama Test Suite Completed!")
    print(f"⏱️  Total execution time: {total_time_message}")
    print(f"🧪 Total tests run: {len(prompts)}")
    print("=" * 60)
