from typing import Optional, Dict, Any
from flask import g
from langchain_core.tools import tool
import logging
from datetime import datetime

from utils.utils import parse_integer_from_string

logger = logging.getLogger(__name__)

@tool
def calculate_total_irrigation_volume_next_x_days(days: Optional[int] = 5) -> dict:
    """
    Calculate the total irrigation volume (in mm or liters) recommended for all fields combined over the next X days.

    USE THIS TOOL WHEN:
    - The user asks about total water needs for all fields
    - The user wants to know the combined irrigation volume for planning
    - The user needs to estimate total water requirements for a period
    - The user asks how much water they'll need in total for irrigation

    DO NOT USE THIS TOOL WHEN:
    - The user asks about a specific field's irrigation needs (use field-specific tools)
    - The user asks about irrigation for a specific date (use date-specific tools)
    - The user asks which fields need irrigation (use field identification tools)
    - The user asks about soil moisture rather than irrigation volume

    EXAMPLE QUERIES:
    - "What is the total irrigation volume needed for all fields in the next 5 days?"
    - "How much water will I need in total for irrigation this week?"
    - "Calculate the total water requirements for all my fields for the next 10 days"
    - "What's the combined irrigation volume I should plan for?"

    Args:
        days (int, optional): The number of future days to calculate total irrigation volume for.
                             Default is 5. Must be a positive integer.

    Returns:
        dict: The total irrigation volume recommended for all fields in the specified period,
              with a breakdown by field if available, or an error message if data retrieval fails.
    """
    try:
        # Convert days to integer if it's a string
        if isinstance(days, str):
            days = int(days)

        # Validate days parameter
        if not isinstance(days, int):
            return {
                "error": "invalid_parameter",
                "message": "The 'days' parameter must be an integer.",
                "details": f"Received type: {type(days)}"
            }
        
        if days <= 0:
            return {
                "error": "invalid_parameter",
                "message": "The 'days' parameter must be a positive integer.",
                "details": f"Received value: {days}"
            }

        # Log the API call
        logger.info(f"Calling irrigation API for next {days} days")
        
        # Call the API with the validated days parameter
        response = g.seabex_api.tools().irrigations().call_tool(
            'calculate_total_irrigation_volume_next_x_days',
            {'days': days}
        )

        # Log the API response
        logger.debug(f"API Response: {response}")

        # Handle error response
        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            logger.error(f"API returned error: {error_message}")
            return {
                "error": "api_error",
                "message": f"Error from irrigation service: {error_message}",
                "details": "The irrigation service encountered an error while processing your request."
            }

        # Handle empty or invalid response
        if not response or not isinstance(response, dict):
            logger.warning(f"Invalid or empty response from API: {response}")
            return {
                "error": "no_data",
                "message": f"No irrigation volume data available for the next {days} days.",
                "details": [
                    "Your fields are in a period of low water consumption",
                    "The weather conditions are favorable for water retention",
                    "The system is still collecting data for this period"
                ],
                "suggestions": [
                    "Try checking individual field status",
                    "Check weather forecasts for the period",
                    "Verify soil moisture levels"
                ]
            }

        # Process and format the response
        if 'total_volume' in response:
            total_volume = float(response['total_volume'])
            fields_data = response.get('fields', [])
            dates_data = response.get('dates', [])
            
            # Format field-specific data if available
            field_details = []
            for field in fields_data:
                if isinstance(field, dict):
                    field_name = field.get('field_name', 'Unknown field')
                    volume = float(field.get('irrigation_volume', 0))
                    if volume > 0:
                        field_details.append({
                            'field_name': field_name,
                            'irrigation_volume': volume,
                            'unit': 'mm'
                        })
            
            # Format date-specific data if available
            date_details = []
            for date_info in dates_data:
                if isinstance(date_info, dict):
                    date = date_info.get('date', '')
                    volume = float(date_info.get('volume', 0))
                    if volume > 0:
                        date_details.append({
                            'date': date,
                            'volume': volume,
                            'unit': 'mm'
                        })
            
            # Construct the response
            result = {
                'total_volume': total_volume,
                'unit': 'mm',
                'period_days': days,
                'fields': field_details,
                'dates': date_details,
                'summary': f"Total irrigation volume needed for all fields in the next {days} days: {total_volume:.2f} mm",
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"Successfully calculated irrigation volume: {total_volume:.2f} mm")
            return result

        # If we get here, the response format is unexpected
        logger.error(f"Unexpected response format: {response}")
        return {
            "error": "invalid_response",
            "message": "The irrigation service returned an unexpected response format.",
            "details": "Please try again later or contact support if the issue persists."
        }

    except ValueError as ve:
        logger.error(f"Value error in irrigation calculation: {str(ve)}")
        return {
            "error": "invalid_input",
            "message": "Invalid input value provided.",
            "details": str(ve)
        }
    except Exception as e:
        logger.error(f"Error calculating total irrigation volume: {str(e)}", exc_info=True)
        return {
            "error": "system_error",
            "message": "I'm sorry, I couldn't calculate the irrigation volume at the moment.",
            "details": "Please try again later or contact support if the issue persists."
        }
