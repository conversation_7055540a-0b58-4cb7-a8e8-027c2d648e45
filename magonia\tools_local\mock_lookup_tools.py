"""
Mock lookup tools for local Ollama testing.
"""

from typing import Optional
from langchain_core.tools import tool

# Mock agricultural knowledge base
AGRICULTURAL_KNOWLEDGE = {
    "cultures_supportées": {
        "céréales": ["<PERSON><PERSON>", "<PERSON>ge", "<PERSON><PERSON>ine", "<PERSON><PERSON><PERSON>", "<PERSON>iz"],
        "légumes": ["Tomates", "Pommes de terre", "Carottes", "Oignons", "Poivrons"],
        "fruits": ["Olives", "Oranges", "Citrons", "Pommes", "Raisins"],
        "légumineuses": ["Haricots", "Pois chiches", "Lentilles", "Fèves"],
        "cultures_industrielles": ["Coton", "Tournesol", "Colza", "Betterave sucrière"]
    },
    "irrigation_techniques": {
        "goutte_à_goutte": "Système d'irrigation très efficace qui économise l'eau en la distribuant directement aux racines",
        "aspersion": "Système qui simule la pluie naturelle, adapté aux grandes surfaces",
        "micro_aspersion": "Système intermédiaire entre goutte-à-goutte et aspersion",
        "irrigation_gravitaire": "Système traditionnel utilisant la gravité pour distribuer l'eau"
    },
    "best_practices": {
        "timing": "Le meilleur moment pour irriguer est tôt le matin (6h-8h) ou en fin de journée (18h-20h)",
        "frequency": "La fréquence d'irrigation dépend du type de sol, de la culture et des conditions météorologiques",
        "water_quality": "Utilisez de l'eau de bonne qualité, testez régulièrement le pH et la salinité"
    }
}

@tool
def lookup_document_tool(query: str) -> str:
    """
    Search agricultural knowledge base for information.
    
    Args:
        query: Search query about agricultural topics
        
    Returns:
        str: Relevant agricultural information
    """
    try:
        query_lower = query.lower()
        
        # Check for supported crops query
        if any(word in query_lower for word in ["cultures", "supportées", "crops", "supported", "plateforme", "seabex"]):
            result = "🌾 Cultures supportées par la plateforme Seabex:\n\n"
            
            for category, crops in AGRICULTURAL_KNOWLEDGE["cultures_supportées"].items():
                result += f"📂 {category.title()}:\n"
                for crop in crops:
                    result += f"  • {crop}\n"
                result += "\n"
            
            result += "💡 La plateforme Seabex supporte plus de 25 types de cultures différentes "
            result += "avec des recommandations d'irrigation personnalisées pour chaque type."
            
            return result
        
        # Check for irrigation techniques
        elif any(word in query_lower for word in ["irrigation", "goutte", "aspersion", "techniques"]):
            result = "💧 Techniques d'irrigation supportées:\n\n"
            
            for technique, description in AGRICULTURAL_KNOWLEDGE["irrigation_techniques"].items():
                result += f"🔧 {technique.replace('_', ' ').title()}:\n"
                result += f"   {description}\n\n"
            
            return result
        
        # Check for best practices
        elif any(word in query_lower for word in ["meilleures", "pratiques", "conseils", "best", "practices"]):
            result = "🎯 Meilleures pratiques d'irrigation:\n\n"
            
            for practice, advice in AGRICULTURAL_KNOWLEDGE["best_practices"].items():
                result += f"📋 {practice.replace('_', ' ').title()}:\n"
                result += f"   {advice}\n\n"
            
            return result
        
        # Check for field tracing
        elif any(word in query_lower for word in ["tracer", "parcelle", "trace", "field"]):
            return """🗺️ Comment tracer votre parcelle sur Seabex:

1. 📱 Connectez-vous à votre compte Seabex
2. 🗺️ Accédez à la section "Mes Parcelles"
3. ➕ Cliquez sur "Ajouter une parcelle"
4. 📍 Utilisez l'outil de traçage pour délimiter votre champ
5. 📝 Renseignez les informations de la parcelle (nom, culture, etc.)
6. ✅ Validez et sauvegardez votre parcelle

💡 Conseil: Utilisez le mode satellite pour une meilleure précision du traçage."""
        
        # Check for production cycle
        elif any(word in query_lower for word in ["cycle", "production", "bilan", "hydrique"]):
            return """🔄 Création d'un cycle de production et activation du bilan hydrique:

1. 📋 Accédez à la section "Cycles de production"
2. ➕ Créez un nouveau cycle
3. 🌱 Sélectionnez le type de culture
4. 📅 Définissez les dates de semis et de récolte
5. 💧 Activez le calcul du bilan hydrique
6. ⚙️ Configurez les paramètres d'irrigation
7. ✅ Lancez le cycle de production

💡 Le bilan hydrique sera calculé automatiquement en fonction des données météorologiques et des caractéristiques de votre sol."""
        
        # Default response for other queries
        else:
            return f"""🔍 Recherche pour: "{query}"

📚 Informations agricoles disponibles:
• Cultures supportées par Seabex
• Techniques d'irrigation
• Meilleures pratiques agricoles
• Guide de traçage des parcelles
• Gestion des cycles de production

💡 Pour des informations plus spécifiques, reformulez votre question en utilisant des mots-clés comme "cultures supportées", "irrigation", "tracer parcelle", etc."""
        
    except Exception as e:
        return f"Erreur lors de la recherche: {str(e)}"
