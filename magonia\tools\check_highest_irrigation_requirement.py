from typing import Optional
from flask import g
from langchain_core.tools import tool

@tool
def check_highest_irrigation_requirement(tool_input: Optional[str] = None):
    """
    Identify the field with the highest irrigation requirement over the next week to help prioritize water allocation.

    USE THIS TOOL WHEN:
    - The user asks which field needs the most water
    - The user wants to know which field has the highest irrigation requirement
    - The user needs to prioritize irrigation resources
    - The user asks about maximum water requirements for planning
    - The user wants to know where to focus irrigation efforts

    DO NOT USE THIS TOOL WHEN:
    - The user asks about today's irrigation only (use check_today_active_irrigation_user)
    - The user asks about a specific field (use field-specific tools)
    - The user asks about soil moisture, not irrigation (use soil moisture tools)
    - The user wants information about all fields (use other irrigation tools)

    EXAMPLE QUERIES:
    - "Which field needs the most water?"
    - "What's my field with the highest irrigation requirement?"
    - "Where should I prioritize my irrigation resources?"
    - "Which area has the greatest water needs?"

    Args:
        tool_input (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        str: A detailed summary of the field with the highest irrigation requirement,
             including field name, irrigation volume, and relevant dates, or an error message
             if data retrieval fails.
    """

    try:

        response = g.seabex_api.tools().irrigations().call_tool(
            'check_highest_irrigation_requirement'
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return f"Error: {error_message}"


        if response:
            # Process the response to make it more user-friendly
            if isinstance(response, dict):
                # Handle nested data structure if present
                data = response.get('data', response)

                field_name = data.get('field_name', 'Unknown field')
                # Try multiple possible keys for irrigation volume
                irrigation_volume = (
                    data.get('highest_irrigation_volume') or
                    data.get('irrigation_volume') or
                    'Unknown'
                )
                period_start = data.get('period_start', 'Unknown')
                period_end = data.get('period_end', 'Unknown')

                # Format the response
                result = (
                    f"Field with highest irrigation requirement: {field_name}\n\n"
                    f"• Required irrigation volume: {irrigation_volume} mm\n"
                    f"• Period: {period_start} to {period_end}\n\n"
                    f"This field should be prioritized in your irrigation planning to ensure optimal crop growth."
                )
                return result

            # If response is a string or other format, return it directly
            return response
        else:
            return "All your fields have low irrigation requirements right now - your crops are well-watered!"

    except Exception as e:
        print(f"Error retrieving irrigation data: {e}")
        return (
            "I'm sorry, I couldn't retrieve the irrigation information at the moment. "
            "Please try again later."
        )
