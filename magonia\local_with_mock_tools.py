"""
Local Ollama Devstral implementation with mock tools for testing.
This version uses mock tools that don't require Flask context or API connections.
"""

import json
import re
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime

# Import mock tools
from tools_local.mock_irrigation_tools import (
    get_current_irrigation_status,
    get_all_user_areas_with_children,
    check_irrigation_user_data,
    check_today_active_irrigation_user,
    check_irrigation_need_for_x_days
)
from tools_local.mock_time_tools import (
    get_current_time_date,
    get_tomorrow,
    get_next_week,
    get_next_month
)
from tools_local.mock_lookup_tools import lookup_document_tool

# Import memory tools (these should work with our Redis fallback)
from magonia.tools.memory_tools import add_memory, get_memories, delete_memory, clear_memories

class OllamaClient:
    """Client for communicating with Ollama API"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.model = "devstral:latest"
    
    def chat(self, messages: List[Dict], tools: List[Dict] = None) -> str:
        """Send chat request to Ollama"""
        try:
            url = f"{self.base_url}/api/chat"
            
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False
            }
            
            if tools:
                payload["tools"] = tools
            
            response = requests.post(url, json=payload, timeout=120)
            response.raise_for_status()
            
            result = response.json()
            return result.get("message", {}).get("content", "")
            
        except requests.exceptions.RequestException as e:
            return f"Error communicating with Ollama: {str(e)}"
        except Exception as e:
            return f"Unexpected error: {str(e)}"

class OllamaDevstralToolsMock:
    """Ollama Devstral implementation with mock tools for testing"""
    
    def __init__(self):
        self.ollama_client = OllamaClient()
        self.tools = self._register_tools()
        self.language_keywords = {
            'french': ['bonjour', 'salut', 'comment', 'quoi', 'que', 'qui', 'où', 'quand', 'pourquoi', 'combien', 'est-ce', 'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles', 'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'à', 'avec', 'pour', 'dans', 'sur', 'sous', 'entre', 'chez', 'vers', 'par', 'sans', 'selon', 'malgré', 'pendant', 'depuis', 'jusqu', 'avant', 'après', 'irrigation', 'champ', 'parcelle', 'culture', 'eau', 'sol'],
            'spanish': ['hola', 'cómo', 'qué', 'quién', 'dónde', 'cuándo', 'por', 'para', 'con', 'sin', 'sobre', 'bajo', 'entre', 'hacia', 'desde', 'hasta', 'durante', 'antes', 'después', 'yo', 'tú', 'él', 'ella', 'nosotros', 'vosotros', 'ellos', 'ellas', 'el', 'la', 'los', 'las', 'un', 'una', 'unos', 'unas', 'riego', 'campo', 'cultivo', 'agua', 'suelo'],
            'tunisian_arabic': ['أهلا', 'مرحبا', 'كيف', 'شنوة', 'وين', 'وقتاش', 'علاش', 'قداش', 'أنا', 'إنت', 'هو', 'هي', 'إحنا', 'إنتوما', 'هوما', 'الري', 'الحقل', 'الزراعة', 'الماء', 'التربة', 'يلزمني', 'نسقي', 'متاعي', 'لوي', 'نهار'],
            'english': ['hello', 'hi', 'how', 'what', 'who', 'where', 'when', 'why', 'which', 'i', 'you', 'he', 'she', 'we', 'they', 'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'with', 'by', 'from', 'about', 'into', 'through', 'during', 'before', 'after', 'irrigation', 'field', 'crop', 'water', 'soil']
        }
        print(f"Registered {len(self.tools)} mock tools for Ollama Devstral")
        print("OllamaDevstralToolsMock initialized - Translation: True, Context Analysis: True")
    
    def _register_tools(self) -> List[Dict]:
        """Register all available mock tools"""
        tools = []
        
        # Irrigation tools
        tools.extend([
            self._tool_to_dict(get_current_irrigation_status),
            self._tool_to_dict(get_all_user_areas_with_children),
            self._tool_to_dict(check_irrigation_user_data),
            self._tool_to_dict(check_today_active_irrigation_user),
            self._tool_to_dict(check_irrigation_need_for_x_days),
        ])
        
        # Time tools
        tools.extend([
            self._tool_to_dict(get_current_time_date),
            self._tool_to_dict(get_tomorrow),
            self._tool_to_dict(get_next_week),
            self._tool_to_dict(get_next_month),
        ])
        
        # Lookup tools
        tools.append(self._tool_to_dict(lookup_document_tool))
        
        # Memory tools
        tools.extend([
            self._tool_to_dict(add_memory),
            self._tool_to_dict(get_memories),
            self._tool_to_dict(delete_memory),
            self._tool_to_dict(clear_memories),
        ])
        
        return tools
    
    def _tool_to_dict(self, tool) -> Dict:
        """Convert LangChain tool to Ollama tool format"""
        return {
            "type": "function",
            "function": {
                "name": tool.name,
                "description": tool.description,
                "parameters": {
                    "type": "object",
                    "properties": tool.args_schema.schema().get("properties", {}) if hasattr(tool, 'args_schema') and tool.args_schema else {},
                    "required": []
                }
            }
        }
    
    def detect_language(self, text: str) -> str:
        """Enhanced language detection with better accuracy"""
        text_lower = text.lower().strip()
        scores = {lang: 0 for lang in self.language_keywords}

        # Enhanced scoring with word boundaries and weights
        for lang, keywords in self.language_keywords.items():
            for keyword in keywords:
                # Exact word matches get higher scores
                if f" {keyword} " in f" {text_lower} ":
                    scores[lang] += 3
                # Partial matches get lower scores
                elif keyword in text_lower:
                    scores[lang] += 1

        # Special patterns for better detection
        french_patterns = ['qu\'', 'est-ce', 'aujourd\'hui', 'parcelle', 'irrigation']
        english_patterns = ['what', 'today', 'field', 'irrigation', 'current']

        for pattern in french_patterns:
            if pattern in text_lower:
                scores['french'] += 2

        for pattern in english_patterns:
            if pattern in text_lower:
                scores['english'] += 2

        print(f"Enhanced language detection: {max(scores, key=scores.get)} (scores: {scores})")
        detected_lang = max(scores, key=scores.get) if max(scores.values()) > 0 else 'english'
        print(f"Detected language: {detected_lang}")
        return detected_lang
    
    def translate_to_language(self, text: str, target_language: str) -> str:
        """Simple translation for common responses"""
        translations = {
            'french': {
                "I'm sorry, I couldn't retrieve the irrigation information at the moment. Please try again later.": 
                "Je suis désolé, je n'ai pas pu récupérer l'information sur l'irrigation à ce moment-là. Veuillez essayer plus tard.",
                "I'm sorry, I couldn't retrieve the irrigation recommendation at the moment. Please try again later or provide more details.":
                "Je suis désolé, je n'ai pas pu récupérer la recommandation d'irrigation à ce moment-là. Veuillez essayer de nouveau plus tard ou fournir plus de détails.",
            },
            'spanish': {
                "I'm sorry, I couldn't retrieve the irrigation information at the moment. Please try again later.": 
                "Lo siento, no pude obtener la información de riego en este momento. Por favor, inténtelo de nuevo más tarde.",
            }
        }
        
        if target_language in translations and text in translations[target_language]:
            return translations[target_language][text]
        return text
    
    def execute_tool(self, tool_name: str, args: Dict) -> str:
        """Execute a tool by name with given arguments"""
        try:
            # Find the tool function
            tool_functions = {
                'get_current_irrigation_status': get_current_irrigation_status,
                'get_all_user_areas_with_children': get_all_user_areas_with_children,
                'check_irrigation_user_data': check_irrigation_user_data,
                'check_today_active_irrigation_user': check_today_active_irrigation_user,
                'check_irrigation_need_for_x_days': check_irrigation_need_for_x_days,
                'get_current_time_date': get_current_time_date,
                'get_tomorrow': get_tomorrow,
                'get_next_week': get_next_week,
                'get_next_month': get_next_month,
                'lookup_document_tool': lookup_document_tool,
                'add_memory': add_memory,
                'get_memories': get_memories,
                'delete_memory': delete_memory,
                'clear_memories': clear_memories,
            }
            
            if tool_name not in tool_functions:
                return f"Tool '{tool_name}' not found"
            
            tool_function = tool_functions[tool_name]
            
            print(f"Executing tool: {tool_name} with args: {args}")

            # Add user_id if the tool supports it and it's not already provided
            # Check if tool function has user_id parameter (for memory tools)
            try:
                if hasattr(tool_function, 'func') and hasattr(tool_function.func, '__code__'):
                    # For LangChain tools
                    if 'user_id' in tool_function.func.__code__.co_varnames and 'user_id' not in args:
                        args['user_id'] = 'mock_user_123'
                elif hasattr(tool_function, '__code__'):
                    # For regular functions
                    if 'user_id' in tool_function.__code__.co_varnames and 'user_id' not in args:
                        args['user_id'] = 'mock_user_123'
            except:
                pass  # Skip if we can't inspect the function

            # Execute the tool - handle LangChain tools differently
            try:
                if hasattr(tool_function, 'invoke'):
                    # LangChain tool - use invoke method
                    if args:
                        result = tool_function.invoke(args)
                    else:
                        result = tool_function.invoke({})
                else:
                    # Regular function
                    if args:
                        result = tool_function(**args)
                    else:
                        result = tool_function()
            except Exception as tool_error:
                # Fallback: try calling with tool_input parameter for LangChain tools
                try:
                    if args:
                        result = tool_function(tool_input=json.dumps(args))
                    else:
                        result = tool_function(tool_input="")
                except:
                    raise tool_error
            
            print(f"Tool execution result: {result}")
            return str(result)
            
        except Exception as e:
            error_msg = f"Error executing tool {tool_name}: {str(e)}"
            print(error_msg)
            return error_msg
    
    def ask(self, question: str, chat_history: str = "", user_id: str = None) -> str:
        """Ask a question using Ollama Devstral with mock tools"""
        try:
            # Detect language
            detected_language = self.detect_language(question)
            
            # Prepare dynamic system prompt based on detected language
            if detected_language == 'french':
                system_prompt = """Tu es Magonia, un assistant agricole expert spécialisé dans l'irrigation et la gestion des cultures.

INSTRUCTIONS CRITIQUES:
1. Tu DOIS TOUJOURS utiliser les outils disponibles pour répondre aux questions
2. Réponds TOUJOURS en français
3. Fournis des réponses PRÉCISES et EXACTES avec les données des outils
4. Formate tes réponses professionnellement avec des emojis et une structure claire

UTILISATION DES OUTILS:
- Parcelles/champs → get_all_user_areas_with_children
- État d'irrigation → get_current_irrigation_status ou check_today_active_irrigation_user
- Cultures supportées → lookup_document_tool avec "cultures supportées"
- Heure/date → get_current_time_date

Format JSON pour les outils:
{"name": "nom_outil", "arguments": {"param": "valeur"}}

UTILISE TOUJOURS les outils appropriés avant de répondre."""
            else:
                system_prompt = """You are Magonia, an expert agricultural assistant specialized in irrigation and crop management.

CRITICAL INSTRUCTIONS:
1. You MUST ALWAYS use available tools to answer questions
2. ALWAYS respond in English
3. Provide PRECISE and EXACT answers with data from tools
4. Format responses professionally with emojis and clear structure

TOOL USAGE:
- Fields/parcels → get_all_user_areas_with_children
- Irrigation status → get_current_irrigation_status or check_today_active_irrigation_user
- Supported crops → lookup_document_tool with "cultures supportées"
- Time/date → get_current_time_date

JSON format for tools:
{"name": "tool_name", "arguments": {"param": "value"}}

ALWAYS use appropriate tools before responding."""
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question}
            ]
            
            print(f"Sending request to Ollama with {len(messages)} messages and {len(self.tools)} tools")
            
            # Get response from Ollama
            response = self.ollama_client.chat(messages, self.tools)
            print(f"Ollama response: {response}")
            
            # Check if response contains tool calls - multiple patterns
            tool_call_patterns = [
                r'```(?:json)?\s*\{\s*"name":\s*"([^"]+)"[^}]*"arguments":\s*(\{[^}]*\})\s*\}\s*```',
                r'\{\s*"name":\s*"([^"]+)"[^}]*"arguments":\s*(\{[^}]*\})\s*\}',
                r'```(?:json)?\s*\{\s*"name":\s*"([^"]+)"[^}]*"arguments":\s*(\{[^}]*\})\s*\}',
            ]

            tool_matches = []
            for pattern in tool_call_patterns:
                matches = re.findall(pattern, response, re.DOTALL)
                if matches:
                    tool_matches.extend(matches)
                    break

            # If no tool calls detected, try to determine which tool to use based on question content
            if not tool_matches:
                tool_matches = self._determine_tool_from_question(question)
            
            if tool_matches:
                for tool_name, args_str in tool_matches:
                    try:
                        args = json.loads(args_str) if args_str.strip() != '{}' else {}
                        print(f"Executing determined tool: {tool_name} with args: {args}")
                        tool_result = self.execute_tool(tool_name, args)
                        
                        # Translate tool result if needed
                        if detected_language != 'english':
                            translated_result = self.translate_to_language(tool_result, detected_language)
                            if translated_result != tool_result:
                                print(f"Translated tool response from English to {detected_language}: {translated_result}")
                                tool_result = translated_result
                        
                        # Generate final response based on tool result
                        final_response = self._generate_agricultural_response(question, tool_result, detected_language, user_id)
                        return final_response
                        
                    except json.JSONDecodeError:
                        print(f"Failed to parse tool arguments: {args_str}")
                        continue
            
            # If no tools were called, generate a direct response
            return self._generate_agricultural_response(question, response, detected_language, user_id)
            
        except Exception as e:
            error_msg = f"Error processing question: {str(e)}"
            print(error_msg)
            return error_msg

    def _determine_tool_from_question(self, question: str) -> List[tuple]:
        """Determine which tool to use based on question content"""
        question_lower = question.lower()

        # Field listing questions
        if any(word in question_lower for word in ['parcelles', 'champs', 'fields', 'mes parcelles', 'quelles sont']):
            return [('get_all_user_areas_with_children', '{}')]

        # Irrigation status questions
        elif any(word in question_lower for word in ['irrigation', 'irriguer', 'arroser', 'besoin', 'need']):
            if any(word in question_lower for word in ['aujourd', 'today', 'maintenant', 'now']):
                return [('check_today_active_irrigation_user', '{}')]
            else:
                return [('get_current_irrigation_status', '{}')]

        # Supported crops questions
        elif any(word in question_lower for word in ['cultures', 'supportées', 'crops', 'supported', 'plateforme', 'seabex']):
            return [('lookup_document_tool', '{"query": "cultures supportées"}')]

        # Time-related questions
        elif any(word in question_lower for word in ['date', 'time', 'heure', 'temps']):
            return [('get_current_time_date', '{}')]

        return []

    def _generate_agricultural_response(self, question: str, tool_result: str, language: str, user_id: str = None) -> str:
        """Generate a helpful agricultural response"""
        user_name = user_id or "agriculteur"
        
        if language == 'french':
            if "Error" in tool_result or "couldn't" in tool_result:
                return f"Bonjour {user_name}! Je suis désolé mais j'ai rencontré un problème technique. Pouvez-vous reformuler votre question ou essayer plus tard?"
            else:
                return f"Bonjour {user_name}! Voici les informations demandées:\n\n{tool_result}"
        elif language == 'spanish':
            if "Error" in tool_result or "couldn't" in tool_result:
                return f"¡Hola {user_name}! Lo siento, pero encontré un problema técnico. ¿Puede reformular su pregunta o intentar más tarde?"
            else:
                return f"¡Hola {user_name}! Aquí está la información solicitada:\n\n{tool_result}"
        else:  # English
            if "Error" in tool_result or "couldn't" in tool_result:
                return f"Hello {user_name}! I'm sorry, but I encountered a technical issue. Could you rephrase your question or try again later?"
            else:
                return f"Hello {user_name}! Here's the information you requested:\n\n{tool_result}"

# Create a global instance for easy import
ollama_devstral_tools_mock = OllamaDevstralToolsMock()

def ask_ollama_devstral_mock(question: str, chat_history: str = "", user_id: str = None) -> str:
    """Convenience function to ask a question using Ollama Devstral with mock tools."""
    return ollama_devstral_tools_mock.ask(question, chat_history, user_id)
