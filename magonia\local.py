"""
Local Ollama Devstral implementation with tool calling capabilities.
This provides a simple approach to use Ollama's Devstral model with tools without any agent framework.
"""

import os
import json
import re
import inspect
import traceback
import requests
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime

from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

# Import tools
try:
    # Try absolute imports first (when run from parent directory)
    from magonia.tools.time import get_current_time_date, get_next_month, get_next_week, get_tomorrow
    from magonia.tools.memory_tools import add_memory, get_memories, delete_memory, clear_memories
    from magonia.tools.lookup_document_tool import lookup_document_tool
    from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
    from magonia.tools.check_irrigation_user_data import check_irrigation_user_data
    from magonia.tools.get_lowest_soil_water_volume import get_lowest_soil_water_volume
    from magonia.tools.calculate_total_irrigation_volume_next_x_days import calculate_total_irrigation_volume_next_x_days
    from magonia.tools.check_irrigation_need_for_x_days import check_irrigation_need_for_x_days
    from magonia.tools.advise_stop_over_irrigation import advise_stop_over_irrigation
    from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
    from magonia.tools.fields_exceeding_water_capacity_for_x_days import fields_exceeding_water_capacity_for_x_days
    from magonia.tools.fields_with_highest_evapotranspiration_next_x_days import fields_with_highest_evapotranspiration_next_x_days
    from magonia.tools.fields_with_highest_water_requirements_for_x_days import fields_with_highest_water_requirements_for_x_days
    from magonia.tools.fields_with_optimal_soil_moisture_for_x_days import fields_with_optimal_soil_moisture_for_x_days
    from magonia.tools.find_fields_no_irrigation_needs_for_x_days import find_fields_no_irrigation_needs_for_x_days
    from magonia.tools.fields_predicted_to_exceed_water_capacity_for_x_days import fields_predicted_to_exceed_water_capacity_for_x_days
    from magonia.tools.get_all_user_areas_with_children import get_all_user_areas_with_children
    from magonia.tools.get_current_irrigation_status import get_current_irrigation_status
    from magonia.tools.check_irrigation_needs_between_period import check_irrigation_needs_between_period
    from magonia.tools.check_soil_water_volume import check_soil_water_volume
    from magonia.tools.check_earliest_irrigation_dates import check_earliest_irrigation_dates
    from magonia.tools.check_highest_evapotranspiration import check_highest_evapotranspiration
    from magonia.tools.check_future_irrigation_fields import check_future_irrigation_fields
    from magonia.tools.predicted_water_consumption_rate_for_x_days import predicted_water_consumption_rate_for_x_days
    from magonia.tools.total_water_consumption_predicted_for_each_field_x_days import total_water_consumption_predicted_for_each_field_x_days
except ImportError:
    # Fall back to relative imports (when run from magonia directory)
    from tools.time import get_current_time_date, get_next_month, get_next_week, get_tomorrow
    from tools.memory_tools import add_memory, get_memories, delete_memory, clear_memories
    from tools.lookup_document_tool import lookup_document_tool
    from tools.check_today_active_irrigation_user import check_today_active_irrigation_user
    from tools.check_irrigation_user_data import check_irrigation_user_data
    from tools.get_lowest_soil_water_volume import get_lowest_soil_water_volume
    from tools.calculate_total_irrigation_volume_next_x_days import calculate_total_irrigation_volume_next_x_days
    from tools.check_irrigation_need_for_x_days import check_irrigation_need_for_x_days
    from tools.advise_stop_over_irrigation import advise_stop_over_irrigation
    from tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
    from tools.fields_exceeding_water_capacity_for_x_days import fields_exceeding_water_capacity_for_x_days
    from tools.fields_with_highest_evapotranspiration_next_x_days import fields_with_highest_evapotranspiration_next_x_days
    from tools.fields_with_highest_water_requirements_for_x_days import fields_with_highest_water_requirements_for_x_days
    from tools.fields_with_optimal_soil_moisture_for_x_days import fields_with_optimal_soil_moisture_for_x_days
    from tools.find_fields_no_irrigation_needs_for_x_days import find_fields_no_irrigation_needs_for_x_days
    from tools.fields_predicted_to_exceed_water_capacity_for_x_days import fields_predicted_to_exceed_water_capacity_for_x_days
    from tools.get_all_user_areas_with_children import get_all_user_areas_with_children
    from tools.get_current_irrigation_status import get_current_irrigation_status
    from tools.check_irrigation_needs_between_period import check_irrigation_needs_between_period
    from tools.check_soil_water_volume import check_soil_water_volume
    from tools.check_earliest_irrigation_dates import check_earliest_irrigation_dates
    from tools.check_highest_evapotranspiration import check_highest_evapotranspiration
    from tools.check_future_irrigation_fields import check_future_irrigation_fields
    from tools.predicted_water_consumption_rate_for_x_days import predicted_water_consumption_rate_for_x_days
    from tools.total_water_consumption_predicted_for_each_field_x_days import total_water_consumption_predicted_for_each_field_x_days

# Ollama configuration
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
OLLAMA_MODEL = os.getenv("OLLAMA_MODEL", "devstral:latest")

# System prompt for Ollama Devstral (same as GPT-4o)
SYSTEM_PROMPT = """
You are Magonia, an agricultural assistant specializing in irrigation, crop management, and farming best practices. Your purpose is to help farmers and agricultural professionals make better decisions about their crops and irrigation needs.

CRITICAL RULES - READ THIS FIRST:
1. YOU MUST ALWAYS USE TOOLS - THIS IS MANDATORY
2. NEVER respond directly without using tools, regardless of the language used
3. The language of the question (English, French, Arabic, Spanish) DOES NOT MATTER - you MUST use tools if there is a need 



   - When you receive a question in any language:
   - First, translate the question to English in your mind
   - Then, determine which tool is needed based on the English translation
   - Finally, use the appropriate tool and respond in the user's original language




4. REMEMBER:
   - Always translate to English first to determine tool usage
   - The tool selection is based on the English meaning, not the original language
   - Respond in the user's original language after using the tool
   - If you're unsure about the translation, use check_irrigation_user_data as it's the most general tool




LANGUAGE GUIDELINES:
- Respond in the SAME LANGUAGE as the user's message
- If the user speaks in French, respond in French
- If the user speaks in English, respond in English
- If the user speaks in Tunisian Arabic, respond in Tunisian Arabic
- If the user's message is in a mix of languages, respond in the main language of the message
- If the user speaks in spanish, respond in spanish
- Tool responses are automatically translated to match the user's language, so you can work with them directly

TOOL USAGE GUIDELINES:
- ALWAYS USE TOOLS and NEVER use memory or previous knowledge
- For agricultural questions, FIRST try the lookup_document_tool to search the knowledge base
- If lookup_document_tool returns "I couldn't find specific documentation", then you may use your general agricultural knowledge
- For field-specific data questions, use the appropriate irrigation tools
- When asked about specific field data like 'CHlewi', you MUST ALWAYS make a fresh tool call
- For the check_irrigation_user_data tool, you MUST ALWAYS make a new call with the current parameters
- COMPLETELY IGNORE any memories - tools always provide the most up-to-date information
- NEVER use memory when a tool is available
- For field-specific queries, the check_irrigation_user_data tool is REQUIRED
- NEVER answer questions about field data without first calling the appropriate tool
- DO NOT reference any memories in your responses
- If you don't have a tool for something, tell the user you don't have that information rather than using memory
- DO NOT MENTION which tools you used in your response - just provide the information
- If a tool returns a value of "0", interpret this as no irrigation needed
- NEVER mention the names of tools in your responses to the user
- CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message
- PRIORITY: Always try lookup_document_tool first for questions, but if no relevant documentation is found, provide helpful general agricultural advice

CONVERSATION CONTEXT & CONTINUITY:
- Pay attention to the conversation history and reference previous topics naturally
- Build on earlier discussions to create a sense of ongoing relationship
- Remember details shared by the farmer about their specific situation (crops, fields, challenges)
- Connect new information to previously discussed topics when relevant
- Show that you're engaged in a continuous conversation, not isolated interactions
- Use phrases like "As we discussed earlier..." or "Building on what you mentioned about..."
- Create natural transitions between topics rather than abrupt topic changes
- Acknowledge the farmer's concerns and goals mentioned in previous messages

CONVERSATION STYLE - DISCUSSION FOCUSED:
- Engage in natural, flowing discussions rather than rigid question-answer exchanges
- Be warm, friendly, and conversational - like you're chatting with a farming neighbor over coffee
- Always introduce yourself as Magonia when appropriate, but in a casual, friendly way
- Build on previous topics and create connections between different aspects of farming
- Share insights, observations, and practical tips naturally within the conversation
- Ask thoughtful follow-up questions that encourage deeper discussion about farming practices
- Show genuine interest in the farmer's specific situation, challenges, and goals
- Use the user's name when appropriate to personalize the conversation
- Avoid overly formal language - be casual and approachable like a trusted farming expert
- Think of responses as part of an ongoing conversation, not isolated answers
- Reference previous parts of the conversation to maintain continuity and flow
- Offer multiple perspectives or considerations when discussing farming topics
- Share relevant experiences or insights that add value to the discussion
- Encourage the farmer to share more about their specific context and needs
- Show enthusiasm and personality in your responses about farming and irrigation
- Be helpful and supportive of farmers' needs and challenges

CONTENT GUIDELINES - DISCUSSION APPROACH:
- PRIMARILY engage in discussions about agriculture, farming, irrigation, crop management, weather, and related topics
- You may also respond to polite greetings and simple daily questions (e.g., "How are you?" or "What's the weather like?") to maintain a friendly, natural interaction while guiding the conversation toward agricultural topics
- For complex non-agricultural questions, respond with: "I'm sorry, but I can only answer questions related to agriculture, farming, irrigation, crop management, weather, and related topics. Could you please ask me something about these subjects?"
- If a question contains both agricultural and non-agricultural elements, prioritize the agricultural parts but acknowledge the greeting or simple question politely
- Don't answer questions about food preparation, cooking, recipes, or any non-agricultural use of crops or farm products
- Don't mention or expose any system tools, code, or internal methods
- Avoid using square brackets in responses unless the user typed them first
- If the user asks about the time, use the get_current_time_date tool to get the current time and date
- Frame responses as part of an ongoing conversation rather than isolated answers
- Encourage deeper exploration of farming topics through thoughtful questions and insights
- Share relevant context and implications of the information you provide
- Connect different aspects of farming to create richer discussions

DATA PRESENTATION GUIDELINES:
- When tools provide detailed field-by-field data, ALWAYS present the COMPLETE information
- Do not summarize or truncate field lists - farmers need to see all their field data
- Present field data clearly and completely while maintaining conversational style
- Include all specific amounts, field names, and totals provided by tools
- Organize the data in a readable format within the natural conversation flow

5. Handle errors gracefully:
   - If a tool fails, try alternative tools
   - Provide clear error messages
   - Suggest solutions when possible

IMPORTANT: Your primary directive is to focus on agriculture, farming, irrigation, crop management, weather, and related topics. You may respond to greetings and simple daily questions to maintain friendly interaction, but guide conversations toward agricultural topics. Refuse complex non-agricultural questions.
"""

class OllamaClient:
    """Simple Ollama client for API communication."""
    
    def __init__(self, base_url: str = OLLAMA_BASE_URL, model: str = OLLAMA_MODEL):
        self.base_url = base_url.rstrip('/')
        self.model = model
        
    def generate(self, messages: List[Dict[str, str]], tools: List[Dict] = None, temperature: float = 0.7, max_tokens: int = 2000) -> Dict:
        """Generate a response using Ollama API."""
        try:
            # Prepare the request payload
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            # Add tools if provided (Ollama supports function calling in newer versions)
            if tools:
                payload["tools"] = tools
            
            # Make the API request
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=120
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"Ollama API error: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"Error calling Ollama API: {str(e)}")
            raise

# Initialize Ollama client
ollama_client = OllamaClient()

class OllamaDevstralTools:
    """Ollama Devstral implementation with tool calling capabilities."""

    def __init__(self, enable_translation=True, enable_context_analysis=True):
        """Initialize with available tools and performance options."""
        self.tools = self._register_tools()
        self.client = ollama_client
        self.enable_translation = enable_translation
        self.enable_context_analysis = enable_context_analysis
        print(f"OllamaDevstralTools initialized - Translation: {enable_translation}, Context Analysis: {enable_context_analysis}")

    def _register_tools(self) -> Dict[str, Dict[str, Any]]:
        """Register all available tools."""
        tools_dict = {}

        # Register get_all_user_areas_with_children first to prioritize field listing
        tools_dict["get_all_user_areas_with_children"] = {
            "function": get_all_user_areas_with_children,
            "description": "Get a complete list of all your fields and areas. Use this tool when the user asks about their fields, areas, or farm structure.",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Time tools
        tools_dict["get_current_time_date"] = {
            "function": get_current_time_date,
            "description": "Get the current date and time",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_tomorrow"] = {
            "function": get_tomorrow,
            "description": "Get tomorrow's date",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_next_week"] = {
            "function": get_next_week,
            "description": "Get the date one week from today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_next_month"] = {
            "function": get_next_month,
            "description": "Get the date one month from today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Irrigation tools
        tools_dict["check_today_active_irrigation_user"] = {
            "function": check_today_active_irrigation_user,
            "description": "Check which fields need irrigation today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_irrigation_user_data"] = {
            "function": check_irrigation_user_data,
            "description": "Get irrigation data for a specific field on a specific date",
            "parameters": {
                "type": "object",
                "properties": {
                    "field_name": {"type": "string", "description": "Name of the field"},
                    "date_of_calculation": {"type": "string", "description": "Date in YYYY-MM-DD format"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["field_name", "date_of_calculation"]
            }
        }

        tools_dict["get_lowest_soil_water_volume"] = {
            "function": get_lowest_soil_water_volume,
            "description": "Get fields with the lowest soil water volume",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["calculate_total_irrigation_volume_next_x_days"] = {
            "function": calculate_total_irrigation_volume_next_x_days,
            "description": "Calculate total irrigation volume needed for all fields in the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to calculate for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["check_irrigation_need_for_x_days"] = {
            "function": check_irrigation_need_for_x_days,
            "description": "Check irrigation needs for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["advise_stop_over_irrigation"] = {
            "function": advise_stop_over_irrigation,
            "description": "Get advice on stopping over-irrigation",
            "parameters": {
                "type": "object",
                "properties": {
                    "field_name": {"type": "string", "description": "Name of the field"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["field_name"]
            }
        }

        tools_dict["check_highest_irrigation_requirement"] = {
            "function": check_highest_irrigation_requirement,
            "description": "Check which fields have the highest irrigation requirements",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Field analysis tools
        tools_dict["fields_exceeding_water_capacity_for_x_days"] = {
            "function": fields_exceeding_water_capacity_for_x_days,
            "description": "Find fields exceeding water capacity for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_with_highest_evapotranspiration_next_x_days"] = {
            "function": fields_with_highest_evapotranspiration_next_x_days,
            "description": "Find fields with highest evapotranspiration for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_with_highest_water_requirements_for_x_days"] = {
            "function": fields_with_highest_water_requirements_for_x_days,
            "description": "Find fields with highest water requirements for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_with_optimal_soil_moisture_for_x_days"] = {
            "function": fields_with_optimal_soil_moisture_for_x_days,
            "description": "Find fields with optimal soil moisture for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        # Memory tools
        tools_dict["add_memory"] = {
            "function": add_memory,
            "description": "Add a memory for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"},
                    "memory": {"type": "string", "description": "Memory to store"}
                },
                "required": ["user_id", "memory"]
            }
        }

        tools_dict["get_memories"] = {
            "function": get_memories,
            "description": "Get memories for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"}
                },
                "required": ["user_id"]
            }
        }

        tools_dict["delete_memory"] = {
            "function": delete_memory,
            "description": "Delete a memory for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"},
                    "memory_index": {"type": "integer", "description": "Index of the memory to delete"}
                },
                "required": ["user_id", "memory_index"]
            }
        }

        tools_dict["clear_memories"] = {
            "function": clear_memories,
            "description": "Clear all memories for the user",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID"}
                },
                "required": ["user_id"]
            }
        }

        # Document lookup tool (RAG)
        tools_dict["lookup_document_tool"] = {
            "function": lookup_document_tool,
            "description": "Search through agricultural knowledge base and documentation to answer questions about farming practices, Seabex platform features, supported crops, irrigation techniques, soil composition, and agricultural best practices. Use this tool FIRST for agricultural questions to check if specific documentation exists. If no relevant documentation is found, you can then provide general agricultural knowledge.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The question or search query to look up in the knowledge base"}
                },
                "required": ["query"]
            }
        }

        # Additional field analysis tools
        tools_dict["find_fields_no_irrigation_needs_for_x_days"] = {
            "function": find_fields_no_irrigation_needs_for_x_days,
            "description": "Find fields that don't need irrigation for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"}
                },
                "required": ["days"]
            }
        }

        tools_dict["fields_predicted_to_exceed_water_capacity_for_x_days"] = {
            "function": fields_predicted_to_exceed_water_capacity_for_x_days,
            "description": "Find fields predicted to exceed water capacity for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to check for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["get_current_irrigation_status"] = {
            "function": get_current_irrigation_status,
            "description": "Get the current irrigation status for all fields",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_irrigation_needs_between_period"] = {
            "function": check_irrigation_needs_between_period,
            "description": "Check irrigation needs between two dates",
            "parameters": {
                "type": "object",
                "properties": {
                    "start_date": {"type": "string", "description": "Start date in YYYY-MM-DD format"},
                    "end_date": {"type": "string", "description": "End date in YYYY-MM-DD format"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["start_date", "end_date"]
            }
        }

        tools_dict["check_soil_water_volume"] = {
            "function": check_soil_water_volume,
            "description": "Check soil water volume for a specific field",
            "parameters": {
                "type": "object",
                "properties": {
                    "field_name": {"type": "string", "description": "Name of the field"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["field_name"]
            }
        }

        tools_dict["check_earliest_irrigation_dates"] = {
            "function": check_earliest_irrigation_dates,
            "description": "Check the earliest irrigation dates for all fields",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_highest_evapotranspiration"] = {
            "function": check_highest_evapotranspiration,
            "description": "Check which fields have the highest evapotranspiration",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_future_irrigation_fields"] = {
            "function": check_future_irrigation_fields,
            "description": "Check which fields will need irrigation in the future",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["predicted_water_consumption_rate_for_x_days"] = {
            "function": predicted_water_consumption_rate_for_x_days,
            "description": "Get predicted water consumption rate for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to predict for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        tools_dict["total_water_consumption_predicted_for_each_field_x_days"] = {
            "function": total_water_consumption_predicted_for_each_field_x_days,
            "description": "Get total water consumption predicted for each field for the next X days",
            "parameters": {
                "type": "object",
                "properties": {
                    "days": {"type": "integer", "description": "Number of days to predict for"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["days"]
            }
        }

        print(f"Registered {len(tools_dict)} tools for Ollama Devstral")
        return tools_dict

    def _format_tools_for_ollama(self) -> List[Dict[str, Any]]:
        """Format tools for Ollama API (similar to OpenAI format)."""
        ollama_tools = []

        for tool_name, tool_info in self.tools.items():
            ollama_tools.append({
                "type": "function",
                "function": {
                    "name": tool_name,
                    "description": tool_info["description"],
                    "parameters": tool_info["parameters"]
                }
            })

        return ollama_tools

    def _parse_tool_calls_from_response(self, response_text: str) -> List[Dict[str, Any]]:
        """Parse tool calls from Ollama response text."""
        tool_calls = []

        # Look for function call patterns in the response
        # This is a simple implementation - you might need to adjust based on how Devstral formats tool calls
        import re

        # Pattern to match function calls like: function_name(arg1="value1", arg2="value2")
        function_pattern = r'(\w+)\((.*?)\)'
        matches = re.findall(function_pattern, response_text)

        for match in matches:
            function_name = match[0]
            args_str = match[1]

            # Parse arguments (simple implementation)
            args = {}
            if args_str:
                # Split by comma and parse key=value pairs
                arg_pairs = args_str.split(',')
                for pair in arg_pairs:
                    if '=' in pair:
                        key, value = pair.split('=', 1)
                        key = key.strip().strip('"\'')
                        value = value.strip().strip('"\'')
                        args[key] = value

            if function_name in self.tools:
                tool_calls.append({
                    "function": {
                        "name": function_name,
                        "arguments": json.dumps(args)
                    }
                })

        return tool_calls

    def _detect_language(self, text: str) -> str:
        """Fast language detection using simple heuristics."""
        try:
            text_lower = text.lower()

            # Fast heuristic-based language detection
            # French indicators
            french_words = ['est-ce', 'que', 'je', 'dois', 'combien', 'aujourd', 'champs', 'irrigation', 'eau']
            french_score = sum(1 for word in french_words if word in text_lower)

            # Arabic/Tunisian indicators (common transliterations)
            arabic_words = ['ماء', 'حقل', 'ري', 'اليوم']
            arabic_score = sum(1 for word in arabic_words if word in text_lower)

            # Spanish indicators
            spanish_words = ['cuánto', 'necesito', 'riego', 'hoy', 'campo', 'agua']
            spanish_score = sum(1 for word in spanish_words if word in text_lower)

            # English indicators
            english_words = ['do', 'need', 'irrigate', 'today', 'field', 'water', 'how', 'much']
            english_score = sum(1 for word in english_words if word in text_lower)

            # Determine language based on highest score
            scores = {
                'french': french_score,
                'tunisian_arabic': arabic_score,
                'spanish': spanish_score,
                'english': english_score
            }

            detected_language = max(scores, key=scores.get)

            # If no clear winner, default to English
            if scores[detected_language] == 0:
                detected_language = "english"

            print(f"Fast language detection: {detected_language} (scores: {scores})")
            return detected_language

        except Exception as e:
            print(f"Error in language detection: {str(e)}")
            return "english"

    def _execute_tool(self, tool_name: str, tool_args: Dict[str, Any], user_id: str = None, target_language: str = "english") -> Any:
        """Execute a tool with the given arguments and translate the response if needed."""
        print(f"Executing tool: {tool_name} with args: {tool_args}")

        if tool_name not in self.tools:
            error_msg = f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"
            return self._translate_tool_response(error_msg, target_language)

        tool_function = self.tools[tool_name]["function"]

        # Create a copy of the arguments to avoid modifying the original
        execution_args = tool_args.copy()

        try:
            # Add cache-busting timestamp to ALL irrigation-related tools
            irrigation_tools = [
                "check_irrigation_user_data",
                "check_today_active_irrigation_user",
                "get_lowest_soil_water_volume",
                "calculate_total_irrigation_volume_next_x_days",
                "check_irrigation_need_for_x_days",
                "advise_stop_over_irrigation",
                "check_highest_irrigation_requirement",
                "fields_exceeding_water_capacity_for_x_days",
                "fields_with_highest_evapotranspiration_next_x_days",
                "fields_with_highest_water_requirements_for_x_days",
                "fields_with_optimal_soil_moisture_for_x_days",
                "find_fields_no_irrigation_needs_for_x_days",
                "fields_predicted_to_exceed_water_capacity_for_x_days",
                "get_current_irrigation_status",
                "check_irrigation_needs_between_period",
                "check_soil_water_volume",
                "check_earliest_irrigation_dates",
                "check_highest_evapotranspiration",
                "check_future_irrigation_fields",
                "predicted_water_consumption_rate_for_x_days",
                "total_water_consumption_predicted_for_each_field_x_days"
            ]

            if tool_name in irrigation_tools:
                # Only add user_id if the tool accepts it
                tool_params = self.tools[tool_name]["parameters"]["properties"]
                if "user_id" in tool_params and user_id and "user_id" not in execution_args:
                    execution_args["user_id"] = user_id
                    print(f"Added user_id to tool arguments: {execution_args}")

                # Add timestamp to ensure fresh data (this helps avoid caching issues)
                cache_buster = datetime.now().isoformat()
                execution_args["_cache_buster"] = cache_buster
                print(f"Added cache buster to ensure fresh data: {cache_buster}")

                # Special handling for check_irrigation_user_data
                if tool_name == "check_irrigation_user_data":
                    print(f"Executing {tool_name} with fresh data request")
                    # Remove the cache buster before actual execution as it's not a real parameter
                    clean_args = {k: v for k, v in execution_args.items() if k != "_cache_buster"}

                    # For check_irrigation_user_data, we need to handle the special input format
                    if isinstance(clean_args, dict) and len(clean_args) > 0:
                        result = tool_function(clean_args)
                    else:
                        result = tool_function(execution_args.get("field_name", ""), execution_args.get("date_of_calculation", ""))
                else:
                    # For other irrigation tools, remove the cache buster
                    clean_args = {k: v for k, v in execution_args.items() if k != "_cache_buster"}

                    # Execute the tool with clean arguments
                    try:
                        if hasattr(tool_function, "func"):
                            result = tool_function.func(**clean_args)
                        else:
                            result = tool_function(**clean_args)
                    except TypeError as type_e:
                        # If there's a TypeError, it might be because the tool doesn't accept user_id
                        if 'user_id' in clean_args and str(type_e).find('user_id') >= 0:
                            print(f"Tool {tool_name} doesn't accept user_id, removing it and retrying")
                            # Remove user_id and try again
                            clean_args = {k: v for k, v in clean_args.items() if k != "user_id"}
                            if hasattr(tool_function, "func"):
                                result = tool_function.func(**clean_args)
                            else:
                                result = tool_function(**clean_args)
                        else:
                            # For other TypeError issues, raise the exception
                            raise
            # BLOCK ALL MEMORY TOOLS
            elif tool_name in ["add_memory", "get_memories", "delete_memory", "clear_memories"]:
                print(f"⛔ MEMORY TOOL BLOCKED: Refusing to execute memory tool '{tool_name}'")

                # Return a message explaining that memory tools are disabled
                if tool_name == "get_memories":
                    result = "Memory access is disabled. Please use appropriate tools to get fresh data instead of relying on memories."
                elif tool_name == "add_memory":
                    result = "Memory storage is disabled. The system is configured to always use tools for fresh data rather than storing memories."
                else:
                    result = "Memory management is disabled. The system is configured to always use tools for fresh data."

                print(f"Returning message about disabled memory tools: {result}")

            # Default handling for other tools
            else:
                # Execute the tool
                if hasattr(tool_function, "func"):
                    # LangChain tool
                    print(f"Executing LangChain tool with args: {execution_args}")

                    # For tools that might need user_id but don't declare it in their parameters
                    # Store the original execution_args to restore if needed
                    original_args = execution_args.copy()

                    # Check if the tool function accepts user_id parameter
                    try:
                        sig = inspect.signature(tool_function.func)
                        if 'user_id' in sig.parameters and user_id and 'user_id' not in execution_args:
                            execution_args['user_id'] = user_id
                            print(f"Added user_id to LangChain tool arguments: {execution_args}")
                    except Exception as sig_e:
                        print(f"Error checking function signature: {sig_e}")

                    # Try to execute the tool with the arguments
                    try:
                        result = tool_function.func(**execution_args)
                    except TypeError as type_e:
                        # If there's a TypeError, it might be because the tool doesn't accept some parameters
                        # Try to remove parameters that might be causing issues
                        if 'user_id' in execution_args and str(type_e).find('user_id') >= 0:
                            print(f"Removing user_id from arguments and retrying")
                            # Restore original arguments
                            execution_args = original_args
                            result = tool_function.func(**execution_args)
                        else:
                            raise
                else:
                    # Direct function
                    if execution_args:
                        # If it's a non-empty dictionary, pass it as kwargs
                        print(f"Executing function with kwargs: {execution_args}")

                        # For functions that might need user_id but don't declare it in their parameters
                        # Store the original execution_args to restore if needed
                        original_args = execution_args.copy()

                        # Check if the function accepts user_id parameter
                        try:
                            sig = inspect.signature(tool_function)
                            if 'user_id' in sig.parameters and user_id and 'user_id' not in execution_args:
                                execution_args['user_id'] = user_id
                                print(f"Added user_id to function arguments: {execution_args}")
                        except Exception as sig_e:
                            print(f"Error checking function signature: {sig_e}")

                        # Try to execute the function with the arguments
                        try:
                            result = tool_function(**execution_args)
                        except TypeError as type_e:
                            # If there's a TypeError, it might be because the function doesn't accept some parameters
                            # Try to remove parameters that might be causing issues
                            if 'user_id' in execution_args and str(type_e).find('user_id') >= 0:
                                print(f"Removing user_id from arguments and retrying")
                                # Restore original arguments
                                execution_args = original_args
                                result = tool_function(**execution_args)
                            else:
                                raise
                    else:
                        # Otherwise call without arguments
                        print("Executing function without arguments")
                        result = tool_function()

            print(f"Tool execution result: {result}")
            # Translate the result if needed
            translated_result = self._translate_tool_response(str(result), target_language)
            return translated_result
        except Exception as e:
            traceback.print_exc()
            error_msg = f"Error executing tool '{tool_name}': {str(e)}"
            return self._translate_tool_response(error_msg, target_language)

    def _translate_tool_response(self, tool_response: str, target_language: str) -> str:
        """
        Translate tool response to the target language using Ollama.
        If the target language is English, always return the original response in English.
        If the target language is French, Tunisian Arabic, or Spanish, translate accordingly.
        Can be disabled for better performance.
        """
        # Skip translation if disabled or if English
        if not self.enable_translation or target_language == "english" or not tool_response or tool_response.strip() == "":
            return tool_response

        try:
            # Create translation prompt based on target language
            if target_language == "french":
                translation_prompt = f"""Translate the following agricultural tool response to French. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the French translation, no explanations."""
            elif target_language == "spanish":
                translation_prompt = f"""Translate the following agricultural tool response to Spanish. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the Spanish translation, no explanations."""
            elif target_language == "tunisian_arabic":
                translation_prompt = f"""Translate the following agricultural tool response to Tunisian Arabic. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the Tunisian Arabic translation, no explanations."""
            else:
                # Fallback: return the original response
                return tool_response

            # Call Ollama for translation
            messages = [
                {"role": "system", "content": "You are a professional translator specializing in agricultural terminology. Translate accurately while preserving technical terms, field names, dates, and numerical values."},
                {"role": "user", "content": translation_prompt}
            ]

            translation_response = self.client.generate(
                messages=messages,
                temperature=0.1,  # Low temperature for consistent translations
                max_tokens=1000
            )

            translated_text = translation_response.get("message", {}).get("content", "").strip()
            print(f"Translated tool response from English to {target_language}: {translated_text}")
            return translated_text

        except Exception as e:
            print(f"Error translating tool response to {target_language}: {str(e)}")
            # Return original response if translation fails
            return tool_response

    def ask(self, question: str, chat_history: str = "", user_id: str = None) -> str:
        """Process a question and return a response using Ollama Devstral with tool calling."""
        try:
            # Detect the language of the user's question for tool response translation
            detected_language = self._detect_language(question)
            print(f"Detected language: {detected_language}")

            # Prepare the initial system message
            system_message = SYSTEM_PROMPT

            # Add a reminder about identity and tool usage with discussion focus
            tool_reminder = f"""

REMEMBER: You are Magonia, the agricultural assistant. Your responses should be:
1. CONVERSATIONAL and DISCUSSION-FOCUSED - like chatting with a farming neighbor
2. ALWAYS use tools when needed - this is mandatory
3. Respond in {detected_language} to match the user's language
4. Build on conversation history naturally
5. Present complete field data when tools provide it
6. Be warm, friendly, and engaging while staying focused on agriculture

Available tools: {', '.join(self.tools.keys())}

Current user question language: {detected_language}
User ID: {user_id or 'not_provided'}
"""

            # Prepare messages for Ollama
            messages = [
                {"role": "system", "content": system_message + tool_reminder}
            ]

            # Add chat history if provided
            if chat_history:
                # Parse chat history and add to messages
                # Assuming chat_history is a string with alternating user/assistant messages
                history_lines = chat_history.strip().split('\n')
                for line in history_lines:
                    if line.startswith("User: "):
                        messages.append({"role": "user", "content": line[6:]})
                    elif line.startswith("Assistant: "):
                        messages.append({"role": "assistant", "content": line[11:]})

            # Add the current question
            messages.append({"role": "user", "content": question})

            # Get tools formatted for Ollama
            tools = self._format_tools_for_ollama()

            print(f"Sending request to Ollama with {len(messages)} messages and {len(tools)} tools")

            # Make the initial request to Ollama
            response = self.client.generate(
                messages=messages,
                tools=tools,
                temperature=0.7,
                max_tokens=2000
            )

            response_content = response.get("message", {}).get("content", "")
            print(f"Ollama response: {response_content}")

            # Check if the response contains tool calls
            # Note: This is a simplified implementation. Ollama's tool calling format may vary
            # You might need to adjust this based on how Devstral actually formats tool calls

            # For now, let's implement a simple pattern-based tool detection
            # If the response mentions needing to check data or use tools, we'll try to extract and execute them

            # Simple heuristic: if response mentions specific tools or seems to need data, execute relevant tools
            if any(keyword in response_content.lower() for keyword in ['check', 'get', 'find', 'calculate', 'need data', 'irrigation']):
                # Try to determine which tool to use based on the question
                tool_to_use = self._determine_tool_from_question(question, detected_language)

                if tool_to_use:
                    tool_name, tool_args = tool_to_use
                    print(f"Executing determined tool: {tool_name} with args: {tool_args}")

                    # Execute the tool
                    tool_result = self._execute_tool(tool_name, tool_args, user_id, detected_language)

                    # Create a follow-up message with the tool result
                    messages.append({"role": "assistant", "content": response_content})
                    messages.append({"role": "user", "content": f"Tool result: {tool_result}. Please provide a complete response based on this data."})

                    # Get final response
                    final_response = self.client.generate(
                        messages=messages,
                        temperature=0.7,
                        max_tokens=2000
                    )

                    return final_response.get("message", {}).get("content", response_content)

            return response_content

        except Exception as e:
            print(f"Error in ask method: {str(e)}")
            traceback.print_exc()
            return f"I apologize, but I encountered an error while processing your request. Please try again. Error: {str(e)}"

    def _determine_tool_from_question(self, question: str, language: str = "english") -> Optional[tuple]:
        """Determine which tool to use based on the question content."""
        question_lower = question.lower()

        # Time-related questions
        if any(word in question_lower for word in ['time', 'date', 'today', 'now', 'current']):
            return ("get_current_time_date", {})

        # Field listing questions
        if any(word in question_lower for word in ['fields', 'areas', 'list', 'show me', 'what fields']):
            return ("get_all_user_areas_with_children", {})

        # Today's irrigation questions
        if any(word in question_lower for word in ['today', 'irrigation today', 'need irrigation']):
            return ("check_today_active_irrigation_user", {})

        # Specific field questions (look for field names)
        field_patterns = ['chlewi', 'field', 'champ']
        for pattern in field_patterns:
            if pattern in question_lower:
                # Extract field name and try to get today's date
                from datetime import datetime
                today = datetime.now().strftime("%Y-%m-%d")

                # Try to extract field name more precisely
                field_name = "CHlewi"  # Default, but try to extract actual name
                if 'chlewi' in question_lower:
                    field_name = "CHlewi"

                return ("check_irrigation_user_data", {
                    "field_name": field_name,
                    "date_of_calculation": today
                })

        # Water volume questions
        if any(word in question_lower for word in ['water volume', 'soil water', 'lowest']):
            return ("get_lowest_soil_water_volume", {})

        # Future irrigation questions with days
        days_match = re.search(r'(\d+)\s*days?', question_lower)
        if days_match:
            days = int(days_match.group(1))
            if any(word in question_lower for word in ['irrigation', 'water', 'need']):
                return ("check_irrigation_need_for_x_days", {"days": days})

        # Default to general irrigation status
        return ("get_current_irrigation_status", {})


# Create a global instance for easy import
ollama_devstral_tools = OllamaDevstralTools()

def ask_ollama_devstral(question: str, chat_history: str = "", user_id: str = None) -> str:
    """Convenience function to ask a question using Ollama Devstral."""
    return ollama_devstral_tools.ask(question, chat_history, user_id)
