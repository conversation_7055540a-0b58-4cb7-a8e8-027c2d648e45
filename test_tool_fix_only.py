#!/usr/bin/env python3
"""
Test script to verify the check_highest_irrigation_requirement tool fix
Tests the tool directly without needing API keys or full agent setup
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_tool_fix():
    """Test the tool fix directly"""
    
    print("🔧 Testing check_highest_irrigation_requirement Tool Fix")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
        print("✅ Successfully imported check_highest_irrigation_requirement")
    except ImportError as e:
        print(f"❌ Failed to import tool: {e}")
        return False
    
    # Mock the Flask g object and API response
    class MockSeabexAPI:
        def tools(self):
            return self
        
        def irrigations(self):
            return self
        
        def call_tool(self, tool_name):
            # Return the actual API response format that was causing issues
            print(f"🔄 Mocking API call to: {tool_name}")
            return {
                'data': {
                    'field_name': 'BIR ali 1',
                    'highest_irrigation_volume': '7.908'
                }
            }
    
    class MockG:
        def __init__(self):
            self.seabex_api = MockSeabexAPI()
    
    # Test scenarios
    test_scenarios = [
        {
            'name': 'Current API Format (Nested data with highest_irrigation_volume)',
            'response': {
                'data': {
                    'field_name': 'BIR ali 1',
                    'highest_irrigation_volume': '7.908'
                }
            }
        },
        {
            'name': 'Legacy API Format (Direct with irrigation_volume)',
            'response': {
                'field_name': 'Test Field',
                'irrigation_volume': '5.5'
            }
        },
        {
            'name': 'Mixed Format (Nested data with irrigation_volume)',
            'response': {
                'data': {
                    'field_name': 'Mixed Field',
                    'irrigation_volume': '3.2'
                }
            }
        },
        {
            'name': 'Missing Volume Data',
            'response': {
                'data': {
                    'field_name': 'No Volume Field'
                }
            }
        }
    ]
    
    print("🧪 Testing different API response formats...")
    print("-" * 60)
    
    all_passed = True
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 Test {i}: {scenario['name']}")
        print(f"🔧 API Response: {scenario['response']}")
        
        try:
            # Temporarily replace Flask's g
            import magonia.tools.check_highest_irrigation_requirement as tool_module
            original_g = getattr(tool_module, 'g', None)
            
            # Create custom mock for this scenario
            class CustomMockAPI:
                def tools(self):
                    return self
                def irrigations(self):
                    return self
                def call_tool(self, tool_name):
                    return scenario['response']
            
            class CustomMockG:
                def __init__(self):
                    self.seabex_api = CustomMockAPI()
            
            tool_module.g = CustomMockG()
            
            # Test the tool with proper LangChain invoke
            result = check_highest_irrigation_requirement.invoke({})
            
            print(f"📝 Tool Result: {result}")
            
            # Analyze the result
            expected_field = scenario['response'].get('data', scenario['response']).get('field_name', 'Unknown field')
            expected_volume = (
                scenario['response'].get('data', scenario['response']).get('highest_irrigation_volume') or
                scenario['response'].get('data', scenario['response']).get('irrigation_volume') or
                'Unknown'
            )
            
            # Check if the result contains expected data
            contains_field = expected_field in result
            contains_volume = str(expected_volume) in result if expected_volume != 'Unknown' else 'Unknown' in result
            
            print(f"🔍 Analysis:")
            print(f"   Expected field: {expected_field}")
            print(f"   Expected volume: {expected_volume}")
            print(f"   Contains field: {'✅' if contains_field else '❌'}")
            print(f"   Contains volume: {'✅' if contains_volume else '❌'}")
            
            if contains_field and contains_volume:
                print(f"✅ Test {i} PASSED")
            else:
                print(f"❌ Test {i} FAILED")
                all_passed = False
            
            # Restore original g
            if original_g:
                tool_module.g = original_g
                
        except Exception as e:
            print(f"❌ Test {i} ERROR: {e}")
            all_passed = False
        
        print("-" * 40)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TOOL FIX TEST SUMMARY")
    print("=" * 60)
    
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The tool fix is working correctly")
        print("✅ Tool handles multiple API response formats")
        print("✅ Tool extracts irrigation volumes properly")
        print("✅ Tool provides meaningful field information")
    else:
        print("❌ SOME TESTS FAILED")
        print("⚠️  The tool fix may need additional work")
    
    # Test the specific issue that was reported
    print("\n🎯 SPECIFIC ISSUE TEST:")
    print("Testing the exact scenario from the user's question...")
    
    try:
        import magonia.tools.check_highest_irrigation_requirement as tool_module
        original_g = getattr(tool_module, 'g', None)
        
        # Mock the exact API response that was causing issues
        class ExactMockAPI:
            def tools(self):
                return self
            def irrigations(self):
                return self
            def call_tool(self, tool_name):
                return {
                    'data': {
                        'field_name': 'BIR ali 1',
                        'highest_irrigation_volume': '7.908'
                    }
                }
        
        class ExactMockG:
            def __init__(self):
                self.seabex_api = ExactMockAPI()
        
        tool_module.g = ExactMockG()
        
        result = check_highest_irrigation_requirement.invoke({})
        
        print(f"📝 Result: {result}")
        
        # Check for the specific fix
        if 'BIR ali 1' in result and '7.908' in result:
            # Check if the irrigation volume is no longer "Unknown"
            if '7.908 mm' in result and 'Unknown mm' not in result:
                print("🎉 SPECIFIC ISSUE FIXED!")
                print("✅ Tool now correctly shows: BIR ali 1 needs 7.908 mm")
                print("✅ No more 'Unknown mm' in the irrigation volume")
                print("ℹ️  Period shows 'Unknown' which is expected (API doesn't provide period data)")
            else:
                print("🔍 PARTIAL FIX")
                print("⚠️  Tool shows some data but irrigation volume may still be Unknown")
        elif 'Unknown mm' in result:
            print("❌ SPECIFIC ISSUE NOT FIXED")
            print("⚠️  Tool still shows 'Unknown mm' for irrigation volume")
        else:
            print("🔍 PARTIAL FIX")
            print("⚠️  Tool shows some data but may need verification")
        
        # Restore original g
        if original_g:
            tool_module.g = original_g
            
    except Exception as e:
        print(f"❌ Specific issue test failed: {e}")
    
    return all_passed

def show_before_after():
    """Show the before and after comparison"""
    
    print("\n" + "=" * 60)
    print("📊 BEFORE vs AFTER COMPARISON")
    print("=" * 60)
    
    print("❌ BEFORE (Broken):")
    print("   API Response: {'data': {'field_name': 'BIR ali 1', 'highest_irrigation_volume': '7.908'}}")
    print("   Tool Output: Required irrigation volume: Unknown mm")
    print("   Problem: Tool looked for 'irrigation_volume' but API returned 'highest_irrigation_volume'")
    
    print("\n✅ AFTER (Fixed):")
    print("   API Response: {'data': {'field_name': 'BIR ali 1', 'highest_irrigation_volume': '7.908'}}")
    print("   Tool Output: Required irrigation volume: 7.908 mm")
    print("   Solution: Tool now checks both 'highest_irrigation_volume' and 'irrigation_volume'")
    
    print("\n🔧 Fix Details:")
    print("   • Added support for nested 'data' structure")
    print("   • Added fallback for multiple volume key names")
    print("   • Maintained backward compatibility")
    print("   • Improved error handling")

if __name__ == "__main__":
    try:
        print("🌾 Testing check_highest_irrigation_requirement Tool Fix")
        print("=" * 70)
        
        # Show before/after comparison
        show_before_after()
        
        # Test the fix
        success = test_tool_fix()
        
        if success:
            print("\n🎉 TOOL FIX VERIFICATION COMPLETE!")
            print("✅ The fix is working correctly")
            print("✅ Ready for production use")
        else:
            print("\n⚠️  TOOL FIX NEEDS ATTENTION")
            print("❌ Some tests failed")
            print("🔧 Additional debugging may be needed")
        
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
