#!/usr/bin/env python3
"""
Diagnostic test for Ollama models and irrigation tools
Identifies specific issues with model responses and tool execution
"""

import sys
import os
import time
import requests
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_ollama_connection():
    """Test basic Ollama connection and model availability"""
    
    print("🔧 OLLAMA CONNECTION DIAGNOSTIC")
    print("=" * 50)
    
    ollama_url = "http://localhost:11434"
    
    # Test basic connection
    try:
        response = requests.get(f"{ollama_url}/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Ollama is running on {ollama_url}")
            print(f"📋 Available models:")
            for model in models.get('models', []):
                print(f"   • {model.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to Ollama at {ollama_url}")
        print("💡 Make sure Ollama is running: ollama serve")
        return False
    except Exception as e:
        print(f"❌ Error connecting to Ollama: {e}")
        return False

def test_simple_ollama_request(model_name: str):
    """Test a simple request to Ollama without tools"""
    
    print(f"\n🧪 Testing simple request to {model_name}")
    print("-" * 40)
    
    ollama_url = "http://localhost:11434"
    
    try:
        payload = {
            "model": model_name,
            "messages": [
                {"role": "user", "content": "Hello, can you respond with 'Test successful'?"}
            ],
            "stream": False
        }
        
        start_time = time.time()
        response = requests.post(f"{ollama_url}/api/chat", json=payload, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            message = result.get('message', {}).get('content', 'No content')
            print(f"✅ {model_name} responded successfully")
            print(f"📝 Response: {message}")
            print(f"⏱️  Time: {end_time - start_time:.2f} seconds")
            return True
        else:
            print(f"❌ {model_name} failed with status {response.status_code}")
            print(f"📝 Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing {model_name}: {e}")
        return False

def test_tool_execution():
    """Test tool execution outside of Ollama context"""
    
    print(f"\n🔧 TOOL EXECUTION DIAGNOSTIC")
    print("=" * 50)
    
    try:
        from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
        print("✅ Successfully imported check_highest_irrigation_requirement")
        
        # Test the tool directly
        try:
            result = check_highest_irrigation_requirement.invoke({})
            print(f"✅ Tool executed successfully")
            print(f"📝 Result: {result[:100]}{'...' if len(result) > 100 else ''}")
            return True
        except Exception as e:
            print(f"❌ Tool execution failed: {e}")
            if "Working outside of application context" in str(e):
                print("💡 This is a Flask context issue - tools need Flask app context")
            return False
            
    except ImportError as e:
        print(f"❌ Failed to import tool: {e}")
        return False

def test_ollama_with_tools(model_name: str):
    """Test Ollama model with tool calling"""
    
    print(f"\n🤖 Testing {model_name} with tool calling")
    print("-" * 40)
    
    ollama_url = "http://localhost:11434"
    
    # Simple tool definition
    tools = [{
        "type": "function",
        "function": {
            "name": "get_time",
            "description": "Get the current time",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }]
    
    try:
        payload = {
            "model": model_name,
            "messages": [
                {"role": "user", "content": "What time is it? Use the get_time tool."}
            ],
            "tools": tools,
            "stream": False
        }
        
        start_time = time.time()
        response = requests.post(f"{ollama_url}/api/chat", json=payload, timeout=30)
        end_time = time.time()
        
        if response.status_code == 200:
            result = response.json()
            message = result.get('message', {})
            content = message.get('content', 'No content')
            tool_calls = message.get('tool_calls', [])
            
            print(f"✅ {model_name} responded to tool request")
            print(f"📝 Content: {content}")
            print(f"🔧 Tool calls: {len(tool_calls)}")
            print(f"⏱️  Time: {end_time - start_time:.2f} seconds")
            
            if tool_calls:
                print("🎉 Model attempted to use tools!")
                for call in tool_calls:
                    print(f"   • {call}")
            else:
                print("⚠️  Model did not use tools")
            
            return len(tool_calls) > 0
        else:
            print(f"❌ {model_name} failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing {model_name} with tools: {e}")
        return False

def main():
    """Main diagnostic function"""
    
    print("🌾 OLLAMA IRRIGATION SYSTEM DIAGNOSTIC")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 Diagnosing Ollama models and irrigation tools")
    print("=" * 60)
    
    # Test 1: Ollama connection
    ollama_available = test_ollama_connection()
    
    if not ollama_available:
        print("\n❌ CRITICAL: Ollama is not available")
        print("💡 Please start Ollama service: ollama serve")
        print("💡 Then pull required models:")
        print("   ollama pull devstral:latest")
        print("   ollama pull mistral:latest")
        return
    
    # Test 2: Simple model requests
    models_to_test = ["devstral:latest", "mistral:latest"]
    working_models = []
    
    for model in models_to_test:
        if test_simple_ollama_request(model):
            working_models.append(model)
    
    if not working_models:
        print("\n❌ CRITICAL: No models are responding")
        print("💡 Check if models are properly installed:")
        print("   ollama list")
        return
    
    # Test 3: Tool execution
    tools_working = test_tool_execution()
    
    # Test 4: Models with tool calling
    tool_capable_models = []
    for model in working_models:
        if test_ollama_with_tools(model):
            tool_capable_models.append(model)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    print(f"🔗 Ollama Connection: {'✅ Working' if ollama_available else '❌ Failed'}")
    print(f"🤖 Working Models: {len(working_models)}/{len(models_to_test)}")
    for model in working_models:
        print(f"   ✅ {model}")
    for model in models_to_test:
        if model not in working_models:
            print(f"   ❌ {model}")
    
    print(f"🔧 Tool Execution: {'✅ Working' if tools_working else '❌ Failed'}")
    print(f"🛠️  Tool-Capable Models: {len(tool_capable_models)}/{len(working_models)}")
    for model in tool_capable_models:
        print(f"   ✅ {model}")
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    if not ollama_available:
        print("   1. Start Ollama service: ollama serve")
        print("   2. Install required models:")
        print("      ollama pull devstral:latest")
        print("      ollama pull mistral:latest")
    
    if working_models and not tools_working:
        print("   1. Flask context issue detected")
        print("   2. Tools need to run within Flask application context")
        print("   3. Consider running tests within the Flask app")
    
    if working_models and not tool_capable_models:
        print("   1. Models are not using tools properly")
        print("   2. Check tool definitions and model compatibility")
        print("   3. Verify tool calling format")
    
    if working_models and tool_capable_models:
        print("   ✅ System appears to be working!")
        print("   1. Empty responses might be due to:")
        print("      - Tool execution failures (Flask context)")
        print("      - Model not generating proper responses")
        print("      - Response parsing issues")
    
    print(f"\n🎯 NEXT STEPS:")
    print("   1. Fix any critical issues identified above")
    print("   2. Test with a simpler irrigation question")
    print("   3. Check tool responses in Flask app context")
    print("   4. Verify API connectivity for real data")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ Diagnostic interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
