"""
Mock irrigation tools for local Ollama testing.
These tools provide realistic demo data without requiring Flask context or API connections.
"""

from typing import Optional, Dict, Any
from langchain_core.tools import tool
from datetime import datetime, timedelta
import json

# Mock data for demonstration
MOCK_FIELDS = {
    "CHlewi": {
        "name": "CHlewi",
        "area": 2.5,
        "crop": "Wheat",
        "soil_water_volume": 45.2,
        "irrigation_status": "needs_irrigation",
        "last_irrigation": "2025-06-28",
        "next_irrigation": "2025-07-02"
    },
    "Sidi_Salah1": {
        "name": "Sidi_Salah1", 
        "area": 3.8,
        "crop": "Tomatoes",
        "soil_water_volume": 62.1,
        "irrigation_status": "optimal",
        "last_irrigation": "2025-06-30",
        "next_irrigation": "2025-07-05"
    },
    "Taba1": {
        "name": "Taba1",
        "area": 1.9,
        "crop": "Olives",
        "soil_water_volume": 38.7,
        "irrigation_status": "needs_irrigation",
        "last_irrigation": "2025-06-25",
        "next_irrigation": "2025-07-01"
    },
    "North_Field": {
        "name": "North_Field",
        "area": 4.2,
        "crop": "Corn",
        "soil_water_volume": 55.3,
        "irrigation_status": "optimal",
        "last_irrigation": "2025-06-29",
        "next_irrigation": "2025-07-04"
    }
}

@tool
def get_current_irrigation_status(tool_input: Optional[str] = None) -> str:
    """
    Retrieve the current irrigation status for all fields.
    
    Returns:
        str: Current irrigation status for all fields
    """
    try:
        today = datetime.now().strftime("%Y-%m-%d")
        
        status_report = f"🌾 Current Irrigation Status - {today}\n"
        status_report += "=" * 50 + "\n\n"
        
        active_irrigations = []
        needs_irrigation = []
        optimal_fields = []
        
        for field_name, field_data in MOCK_FIELDS.items():
            if field_data["irrigation_status"] == "needs_irrigation":
                needs_irrigation.append(f"• {field_name} ({field_data['crop']}) - Soil: {field_data['soil_water_volume']}%")
            elif field_data["irrigation_status"] == "optimal":
                optimal_fields.append(f"• {field_name} ({field_data['crop']}) - Soil: {field_data['soil_water_volume']}%")
        
        if needs_irrigation:
            status_report += "🚨 Fields Needing Irrigation:\n"
            status_report += "\n".join(needs_irrigation) + "\n\n"
        
        if optimal_fields:
            status_report += "✅ Fields with Optimal Moisture:\n"
            status_report += "\n".join(optimal_fields) + "\n\n"
        
        status_report += f"📊 Total Fields: {len(MOCK_FIELDS)}\n"
        status_report += f"🔴 Need Irrigation: {len(needs_irrigation)}\n"
        status_report += f"🟢 Optimal: {len(optimal_fields)}\n"
        
        return status_report
        
    except Exception as e:
        return f"Error retrieving irrigation status: {str(e)}"

@tool
def get_all_user_areas_with_children(tool_input: Optional[str] = None) -> str:
    """
    Retrieve all user fields and areas.
    
    Returns:
        str: List of all user fields
    """
    try:
        field_list = "🌾 Your Agricultural Fields\n"
        field_list += "=" * 40 + "\n\n"
        
        for i, (field_name, field_data) in enumerate(MOCK_FIELDS.items(), 1):
            field_list += f"{i}. {field_name}\n"
            field_list += f"   📍 Area: {field_data['area']} hectares\n"
            field_list += f"   🌱 Crop: {field_data['crop']}\n"
            field_list += f"   💧 Soil Moisture: {field_data['soil_water_volume']}%\n"
            field_list += f"   📅 Last Irrigation: {field_data['last_irrigation']}\n\n"
        
        field_list += f"📊 Total Fields: {len(MOCK_FIELDS)}\n"
        field_list += f"📏 Total Area: {sum(f['area'] for f in MOCK_FIELDS.values())} hectares"
        
        return field_list
        
    except Exception as e:
        return f"Error retrieving fields: {str(e)}"

@tool
def check_irrigation_user_data(field_name: str, date_of_calculation: str) -> str:
    """
    Check irrigation data for a specific field on a specific date.
    
    Args:
        field_name: Name of the field
        date_of_calculation: Date in YYYY-MM-DD format
        
    Returns:
        str: Irrigation recommendation for the field
    """
    try:
        if field_name not in MOCK_FIELDS:
            return f"Field '{field_name}' not found. Available fields: {', '.join(MOCK_FIELDS.keys())}"
        
        field_data = MOCK_FIELDS[field_name]
        
        recommendation = f"🌾 Irrigation Analysis for {field_name}\n"
        recommendation += "=" * 40 + "\n\n"
        recommendation += f"📅 Date: {date_of_calculation}\n"
        recommendation += f"🌱 Crop: {field_data['crop']}\n"
        recommendation += f"📏 Area: {field_data['area']} hectares\n"
        recommendation += f"💧 Current Soil Moisture: {field_data['soil_water_volume']}%\n\n"
        
        if field_data["irrigation_status"] == "needs_irrigation":
            water_needed = round(field_data['area'] * 25, 1)  # 25mm per hectare
            recommendation += "🚨 IRRIGATION RECOMMENDED\n"
            recommendation += f"💧 Water needed: {water_needed} cubic meters\n"
            recommendation += f"⏰ Recommended time: Early morning (6-8 AM)\n"
            recommendation += f"🎯 Target: Increase soil moisture to 65-70%\n"
        else:
            recommendation += "✅ NO IRRIGATION NEEDED\n"
            recommendation += "💧 Soil moisture is optimal\n"
            recommendation += f"📅 Next check: {field_data['next_irrigation']}\n"
        
        return recommendation
        
    except Exception as e:
        return f"Error checking irrigation data: {str(e)}"

@tool
def check_today_active_irrigation_user(tool_input: Optional[str] = None) -> str:
    """
    Check which fields need irrigation today.
    
    Returns:
        str: Fields that need irrigation today
    """
    try:
        today = datetime.now().strftime("%Y-%m-%d")
        
        report = f"🌾 Today's Irrigation Needs - {today}\n"
        report += "=" * 45 + "\n\n"
        
        fields_needing_irrigation = []
        
        for field_name, field_data in MOCK_FIELDS.items():
            if field_data["irrigation_status"] == "needs_irrigation":
                water_needed = round(field_data['area'] * 25, 1)
                fields_needing_irrigation.append({
                    'name': field_name,
                    'crop': field_data['crop'],
                    'water': water_needed,
                    'moisture': field_data['soil_water_volume']
                })
        
        if fields_needing_irrigation:
            report += "🚨 Fields requiring irrigation today:\n\n"
            for field in fields_needing_irrigation:
                report += f"• {field['name']} ({field['crop']})\n"
                report += f"  💧 Water needed: {field['water']} cubic meters\n"
                report += f"  📊 Current moisture: {field['moisture']}%\n\n"
            
            total_water = sum(f['water'] for f in fields_needing_irrigation)
            report += f"💧 Total water needed today: {total_water} cubic meters\n"
            report += f"⏰ Best irrigation time: 6:00-8:00 AM\n"
        else:
            report += "✅ No fields require irrigation today!\n"
            report += "All fields have optimal soil moisture levels.\n"
        
        return report
        
    except Exception as e:
        return f"Error checking today's irrigation needs: {str(e)}"

@tool
def check_irrigation_need_for_x_days(days: int) -> str:
    """
    Check irrigation needs for the next X days.
    
    Args:
        days: Number of days to check ahead
        
    Returns:
        str: Irrigation forecast for the next X days
    """
    try:
        today = datetime.now()
        
        report = f"🌾 {days}-Day Irrigation Forecast\n"
        report += "=" * 40 + "\n\n"
        
        for day in range(days):
            check_date = today + timedelta(days=day)
            date_str = check_date.strftime("%Y-%m-%d")
            day_name = check_date.strftime("%A")
            
            report += f"📅 {day_name}, {date_str}\n"
            
            if day == 0:  # Today
                needs_irrigation = [f for f in MOCK_FIELDS.values() if f["irrigation_status"] == "needs_irrigation"]
            elif day <= 2:  # Next 2 days - some fields might need irrigation
                needs_irrigation = [f for f in MOCK_FIELDS.values() if f["soil_water_volume"] < 50]
            else:  # Later days - fewer fields need irrigation
                needs_irrigation = [f for f in MOCK_FIELDS.values() if f["soil_water_volume"] < 40]
            
            if needs_irrigation:
                report += f"🚨 {len(needs_irrigation)} field(s) need irrigation:\n"
                for field in needs_irrigation:
                    water_needed = round(field['area'] * 20, 1)
                    report += f"  • {field['name']} - {water_needed}m³\n"
            else:
                report += "✅ No irrigation needed\n"
            
            report += "\n"
        
        return report
        
    except Exception as e:
        return f"Error checking irrigation forecast: {str(e)}"
