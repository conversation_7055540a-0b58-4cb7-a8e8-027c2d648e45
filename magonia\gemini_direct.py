"""
Direct Google Gemini API implementation with tool calling capabilities.
This provides a simple approach to use <PERSON> with tools without any agent framework.
"""

import os
import json
import re
import inspect
import traceback
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime

import google.generativeai as genai
from google.generativeai.types import FunctionDeclaration, Tool

# Import tools
from magonia.tools.time import get_current_time_date, get_next_month, get_next_week, get_tomorrow
from magonia.tools.memory_tools import add_memory, get_memories, delete_memory, clear_memories
from magonia.tools.lookup_document_tool import lookup_document_tool
from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
from magonia.tools.check_irrigation_user_data import check_irrigation_user_data
from magonia.tools.get_lowest_soil_water_volume import get_lowest_soil_water_volume
from magonia.tools.calculate_total_irrigation_volume_next_x_days import calculate_total_irrigation_volume_next_x_days
from magonia.tools.check_irrigation_need_for_x_days import check_irrigation_need_for_x_days
from magonia.tools.advise_stop_over_irrigation import advise_stop_over_irrigation
from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
from magonia.tools.fields_exceeding_water_capacity_for_x_days import fields_exceeding_water_capacity_for_x_days
from magonia.tools.fields_with_highest_evapotranspiration_next_x_days import fields_with_highest_evapotranspiration_next_x_days
from magonia.tools.fields_with_highest_water_requirements_for_x_days import fields_with_highest_water_requirements_for_x_days
from magonia.tools.fields_with_optimal_soil_moisture_for_x_days import fields_with_optimal_soil_moisture_for_x_days
from magonia.tools.find_fields_no_irrigation_needs_for_x_days import find_fields_no_irrigation_needs_for_x_days
from magonia.tools.fields_predicted_to_exceed_water_capacity_for_x_days import fields_predicted_to_exceed_water_capacity_for_x_days
from magonia.tools.get_all_user_areas_with_children import get_all_user_areas_with_children
from magonia.tools.get_current_irrigation_status import get_current_irrigation_status
from magonia.tools.check_irrigation_needs_between_period import check_irrigation_needs_between_period
from magonia.tools.check_soil_water_volume import check_soil_water_volume
from magonia.tools.check_earliest_irrigation_dates import check_earliest_irrigation_dates
from magonia.tools.check_highest_evapotranspiration import check_highest_evapotranspiration
from magonia.tools.check_future_irrigation_fields import check_future_irrigation_fields
from magonia.tools.predicted_water_consumption_rate_for_x_days import predicted_water_consumption_rate_for_x_days
from magonia.tools.total_water_consumption_predicted_for_each_field_x_days import total_water_consumption_predicted_for_each_field_x_days

# Initialize Gemini
gemini_api_key = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=gemini_api_key)

# System prompt for Gemini
SYSTEM_PROMPT = """
You are Magonia, an agricultural assistant specializing in irrigation, crop management, and farming best practices. Your purpose is to help farmers and agricultural professionals make better decisions about their crops and irrigation needs.

CRITICAL RULES - READ THIS FIRST:
1. YOU MUST ALWAYS USE TOOLS - THIS IS MANDATORY
2. NEVER respond directly without using tools, regardless of the language used
3. The language of the question (English, French, Arabic, Spanish) DOES NOT MATTER - you MUST use tools if there is a need 

   - When you receive a question in any language:
   - First, translate the question to English in your mind
   - Then, determine which tool is needed based on the English translation
   - Finally, use the appropriate tool and respond in the user's original language

4. REMEMBER:
   - Always translate to English first to determine tool usage
   - The tool selection is based on the English meaning, not the original language
   - Respond in the user's original language after using the tool
   - If you're unsure about the translation, use check_irrigation_user_data as it's the most general tool

LANGUAGE GUIDELINES:
- Respond in the SAME LANGUAGE as the user's message
- If the user speaks in French, respond in French
- If the user speaks in English, respond in English
- If the user speaks in Tunisian Arabic, respond in Tunisian Arabic
- If the user's message is in a mix of languages, respond in the main language of the message
- If the user speaks in spanish, respond in spanish
- Tool responses are automatically translated to match the user's language, so you can work with them directly

TOOL USAGE GUIDELINES:
- ALWAYS USE TOOLS and NEVER use memory or previous knowledge
- For agricultural questions, FIRST try the lookup_document_tool to search the knowledge base
- If lookup_document_tool returns "I couldn't find specific documentation", then you may use your general agricultural knowledge
- For field-specific data questions, use the appropriate irrigation tools
- When asked about specific field data like 'CHlewi', you MUST ALWAYS make a fresh tool call
- For the check_irrigation_user_data tool, you MUST ALWAYS make a new call with the current parameters
- COMPLETELY IGNORE any memories - tools always provide the most up-to-date information
- NEVER use memory when a tool is available
- For field-specific queries, the check_irrigation_user_data tool is REQUIRED
- NEVER answer questions about field data without first calling the appropriate tool
- DO NOT reference any memories in your responses
- If you don't have a tool for something, tell the user you don't have that information rather than using memory
- DO NOT MENTION which tools you used in your response - just provide the information
- If a tool returns a value of "0", interpret this as no irrigation needed
- NEVER mention the names of tools in your responses to the user
- CRITICAL: If a tool provides a specific response about 'No field had...' or 'No data available for...', use that EXACT response - do not rephrase it into an apologetic message
- PRIORITY: Always try lookup_document_tool first for questions, but if no relevant documentation is found, provide helpful general agricultural advice

CONVERSATION CONTEXT & CONTINUITY:
- Pay attention to the conversation history and reference previous topics naturally
- Build on earlier discussions to create a sense of ongoing relationship
- Remember details shared by the farmer about their specific situation (crops, fields, challenges)
- Connect new information to previously discussed topics when relevant
- Show that you're engaged in a continuous conversation, not isolated interactions
- Use phrases like "As we discussed earlier..." or "Building on what you mentioned about..."
- Create natural transitions between topics rather than abrupt topic changes
- Acknowledge the farmer's concerns and goals mentioned in previous messages

CONVERSATION STYLE - DISCUSSION FOCUSED:
- Engage in natural, flowing discussions rather than rigid question-answer exchanges
- Be warm, friendly, and conversational - like you're chatting with a farming neighbor over coffee
- Always introduce yourself as Magonia when appropriate, but in a casual, friendly way
- Build on previous topics and create connections between different aspects of farming
- Share insights, observations, and practical tips naturally within the conversation
- Ask thoughtful follow-up questions that encourage deeper discussion about farming practices
- Show genuine interest in the farmer's specific situation, challenges, and goals
- Use the user's name when appropriate to personalize the conversation
- Avoid overly formal language - be casual and approachable like a trusted farming expert
- Think of responses as part of an ongoing conversation, not isolated answers
- Reference previous parts of the conversation to maintain continuity and flow
- Offer multiple perspectives or considerations when discussing farming topics
- Share relevant experiences or insights that add value to the discussion
- Encourage the farmer to share more about their specific context and needs
- Show enthusiasm and personality in your responses about farming and irrigation
- Be helpful and supportive of farmers' needs and challenges

CONTENT GUIDELINES - DISCUSSION APPROACH:
- PRIMARILY engage in discussions about agriculture, farming, irrigation, crop management, weather, and related topics
- You may also respond to polite greetings and simple daily questions (e.g., "How are you?" or "What's the weather like?") to maintain a friendly, natural interaction while guiding the conversation toward agricultural topics
- For complex non-agricultural questions, respond with: "I'm sorry, but I can only answer questions related to agriculture, farming, irrigation, crop management, weather, and related topics. Could you please ask me something about these subjects?"
- If a question contains both agricultural and non-agricultural elements, prioritize the agricultural parts but acknowledge the greeting or simple question politely
- Don't answer questions about food preparation, cooking, recipes, or any non-agricultural use of crops or farm products
- Don't mention or expose any system tools, code, or internal methods
- Avoid using square brackets in responses unless the user typed them first
- If the user asks about the time, use the get_current_time_date tool to get the current time and date
- Frame responses as part of an ongoing conversation rather than isolated answers
- Encourage deeper exploration of farming topics through thoughtful questions and insights
- Share relevant context and implications of the information you provide
- Connect different aspects of farming to create richer discussions

DATA PRESENTATION GUIDELINES:
- When tools provide detailed field-by-field data, ALWAYS present the COMPLETE information
- Do not summarize or truncate field lists - farmers need to see all their field data
- Present field data clearly and completely while maintaining conversational style
- Include all specific amounts, field names, and totals provided by tools
- Organize the data in a readable format within the natural conversation flow

5. Handle errors gracefully:
   - If a tool fails, try alternative tools
   - Provide clear error messages
   - Suggest solutions when possible

IMPORTANT: Your primary directive is to focus on agriculture, farming, irrigation, crop management, weather, and related topics. You may respond to greetings and simple daily questions to maintain friendly interaction, but guide conversations toward agricultural topics. Refuse complex non-agricultural questions.
"""

class GeminiDirectTools:
    """Direct Gemini implementation with tool calling capabilities."""

    def __init__(self, enable_translation=True, enable_context_analysis=True):
        """Initialize with available tools and performance options."""
        self.tools = self._register_tools()
        self.model = genai.GenerativeModel('gemini-1.5-pro-latest')
        self.enable_translation = enable_translation
        self.enable_context_analysis = enable_context_analysis
        print(f"GeminiDirectTools initialized - Translation: {enable_translation}, Context Analysis: {enable_context_analysis}")

    def _register_tools(self) -> Dict[str, Dict[str, Any]]:
        """Register all available tools."""
        tools_dict = {}

        # Register get_all_user_areas_with_children first to prioritize field listing
        tools_dict["get_all_user_areas_with_children"] = {
            "function": get_all_user_areas_with_children,
            "description": "Get a complete list of all your fields and areas. Use this tool when the user asks about their fields, areas, or farm structure.",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Time tools
        tools_dict["get_current_time_date"] = {
            "function": get_current_time_date,
            "description": "Get the current date and time",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_tomorrow"] = {
            "function": get_tomorrow,
            "description": "Get tomorrow's date",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_next_week"] = {
            "function": get_next_week,
            "description": "Get the date one week from today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_next_month"] = {
            "function": get_next_month,
            "description": "Get the date one month from today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Irrigation tools
        tools_dict["check_today_active_irrigation_user"] = {
            "function": check_today_active_irrigation_user,
            "description": "Check which fields need irrigation today",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_irrigation_user_data"] = {
            "function": check_irrigation_user_data,
            "description": "Get irrigation data for a specific field on a specific date",
            "parameters": {
                "type": "object",
                "properties": {
                    "field_name": {"type": "string", "description": "Name of the field"},
                    "date_of_calculation": {"type": "string", "description": "Date in YYYY-MM-DD format"},
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": ["field_name", "date_of_calculation"]
            }
        }

        # Add more irrigation tools
        tools_dict["get_lowest_soil_water_volume"] = {
            "function": get_lowest_soil_water_volume,
            "description": "Get fields with the lowest soil water volume",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["check_highest_irrigation_requirement"] = {
            "function": check_highest_irrigation_requirement,
            "description": "Check which fields have the highest irrigation requirements",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        tools_dict["get_current_irrigation_status"] = {
            "function": get_current_irrigation_status,
            "description": "Get the current irrigation status for all fields",
            "parameters": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "User ID (automatically provided by the system)"}
                },
                "required": []
            }
        }

        # Document lookup tool (RAG)
        tools_dict["lookup_document_tool"] = {
            "function": lookup_document_tool,
            "description": "Search through agricultural knowledge base and documentation to answer questions about farming practices, Seabex platform features, supported crops, irrigation techniques, soil composition, and agricultural best practices. Use this tool FIRST for agricultural questions to check if specific documentation exists. If no relevant documentation is found, you can then provide general agricultural knowledge.",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The question or search query to look up in the knowledge base"}
                },
                "required": ["query"]
            }
        }

        print(f"Registered {len(tools_dict)} tools for Gemini direct")
        return tools_dict

    def _format_tools_for_gemini(self) -> List[Tool]:
        """Format tools for Gemini API."""
        gemini_functions = []

        for tool_name, tool_info in self.tools.items():
            # Convert parameters to Gemini format
            gemini_params = {}
            if "properties" in tool_info["parameters"]:
                for param_name, param_info in tool_info["parameters"]["properties"].items():
                    gemini_params[param_name] = param_info

            function_declaration = FunctionDeclaration(
                name=tool_name,
                description=tool_info["description"],
                parameters=gemini_params
            )
            gemini_functions.append(function_declaration)

        # Return as a single Tool object containing all functions
        return [Tool(function_declarations=gemini_functions)]

    def _execute_tool(self, tool_name: str, tool_args: Dict[str, Any], user_id: str = None, target_language: str = "english") -> Any:
        """Execute a tool with the given arguments and translate the response if needed."""
        print(f"Executing tool: {tool_name} with args: {tool_args}")

        if tool_name not in self.tools:
            error_msg = f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"
            return self._translate_tool_response(error_msg, target_language)

        tool_function = self.tools[tool_name]["function"]

        # Create a copy of the arguments to avoid modifying the original
        execution_args = tool_args.copy()

        try:
            # Add cache-busting timestamp to ALL irrigation-related tools
            irrigation_tools = [
                "check_irrigation_user_data",
                "check_today_active_irrigation_user",
                "get_lowest_soil_water_volume",
                "check_highest_irrigation_requirement",
                "get_current_irrigation_status"
            ]

            if tool_name in irrigation_tools:
                # Only add user_id if the tool accepts it
                tool_params = self.tools[tool_name]["parameters"]["properties"]
                if "user_id" in tool_params and user_id and "user_id" not in execution_args:
                    execution_args["user_id"] = user_id
                    print(f"Added user_id to tool arguments: {execution_args}")

                # Add timestamp to ensure fresh data (this helps avoid caching issues)
                cache_buster = datetime.now().isoformat()
                execution_args["_cache_buster"] = cache_buster
                print(f"Added cache buster to ensure fresh data: {cache_buster}")

                # Remove the cache buster before actual execution as it's not a real parameter
                clean_args = {k: v for k, v in execution_args.items() if k != "_cache_buster"}

                # Execute the tool with clean arguments
                try:
                    if hasattr(tool_function, "func"):
                        result = tool_function.func(**clean_args)
                    else:
                        result = tool_function(**clean_args)
                except TypeError as type_e:
                    # If there's a TypeError, it might be because the tool doesn't accept user_id
                    if 'user_id' in clean_args and str(type_e).find('user_id') >= 0:
                        print(f"Tool {tool_name} doesn't accept user_id, removing it and retrying")
                        # Remove user_id and try again
                        clean_args = {k: v for k, v in clean_args.items() if k != "user_id"}
                        if hasattr(tool_function, "func"):
                            result = tool_function.func(**clean_args)
                        else:
                            result = tool_function(**clean_args)
                    else:
                        # For other TypeError issues, raise the exception
                        raise

            # Default handling for other tools
            else:
                # Execute the tool
                if hasattr(tool_function, "func"):
                    # LangChain tool
                    print(f"Executing LangChain tool with args: {execution_args}")
                    result = tool_function.func(**execution_args)
                else:
                    # Direct function
                    if execution_args:
                        # If it's a non-empty dictionary, pass it as kwargs
                        print(f"Executing function with kwargs: {execution_args}")
                        result = tool_function(**execution_args)
                    else:
                        # Otherwise call without arguments
                        print("Executing function without arguments")
                        result = tool_function()

            print(f"Tool execution result: {result}")
            # Translate the result if needed
            translated_result = self._translate_tool_response(str(result), target_language)
            return translated_result
        except Exception as e:
            traceback.print_exc()
            error_msg = f"Error executing tool '{tool_name}': {str(e)}"
            return self._translate_tool_response(error_msg, target_language)

    def _detect_language(self, text: str) -> str:
        """Fast language detection using simple heuristics."""
        try:
            text_lower = text.lower()

            # Fast heuristic-based language detection
            # French indicators
            french_words = ['est-ce', 'que', 'je', 'dois', 'combien', 'aujourd', 'champs', 'irrigation', 'eau']
            french_score = sum(1 for word in french_words if word in text_lower)

            # Arabic/Tunisian indicators (common transliterations)
            arabic_words = ['ماء', 'حقل', 'ري', 'اليوم']
            arabic_score = sum(1 for word in arabic_words if word in text_lower)

            # Spanish indicators
            spanish_words = ['cuánto', 'necesito', 'riego', 'hoy', 'campo', 'agua']
            spanish_score = sum(1 for word in spanish_words if word in text_lower)

            # English indicators
            english_words = ['do', 'need', 'irrigate', 'today', 'field', 'water', 'how', 'much']
            english_score = sum(1 for word in english_words if word in text_lower)

            # Determine language based on highest score
            scores = {
                'french': french_score,
                'tunisian_arabic': arabic_score,
                'spanish': spanish_score,
                'english': english_score
            }

            detected_language = max(scores, key=scores.get)

            # If no clear winner, default to English
            if scores[detected_language] == 0:
                detected_language = "english"

            print(f"Fast language detection: {detected_language} (scores: {scores})")
            return detected_language

        except Exception as e:
            print(f"Error in language detection: {str(e)}")
            return "english"

    def _translate_tool_response(self, tool_response: str, target_language: str) -> str:
        """
        Translate tool response to the target language using Gemini.
        If the target language is English, always return the original response in English.
        If the target language is French, Tunisian Arabic, or Spanish, translate accordingly.
        Can be disabled for better performance.
        """
        # Skip translation if disabled or if English
        if not self.enable_translation or target_language == "english" or not tool_response or tool_response.strip() == "":
            return tool_response

        try:
            # Create translation prompt based on target language
            if target_language == "french":
                translation_prompt = f"""Translate the following agricultural tool response to French. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the French translation, no explanations."""
            elif target_language == "spanish":
                translation_prompt = f"""Translate the following agricultural tool response to Spanish. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the Spanish translation, no explanations."""
            elif target_language == "tunisian_arabic":
                translation_prompt = f"""Translate the following agricultural tool response to Tunisian Arabic. Keep all technical terms, field names, dates, and numbers exactly as they are. Only translate the descriptive text:

{tool_response}

Provide only the Tunisian Arabic translation, no explanations."""
            else:
                # Fallback: return the original response
                return tool_response

            # Call Gemini for translation
            translation_model = genai.GenerativeModel('gemini-1.5-pro-latest')
            translation_response = translation_model.generate_content(
                translation_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,  # Low temperature for consistent translations
                    max_output_tokens=1000
                )
            )

            translated_text = translation_response.text.strip()
            print(f"Translated tool response from English to {target_language}: {translated_text}")
            return translated_text

        except Exception as e:
            print(f"Error translating tool response to {target_language}: {str(e)}")
            # Return original response if translation fails
            return tool_response

    def ask(self, question: str, chat_history: str = "", user_id: str = None) -> str:
        """Process a question and return a response using Gemini with tool calling."""
        try:
            # Detect the language of the user's question for tool response translation
            detected_language = self._detect_language(question)
            print(f"Detected language: {detected_language}")

            # Prepare the initial system message
            system_message = SYSTEM_PROMPT

            # Add a reminder about identity and tool usage with discussion focus
            tool_reminder = f"""
CRITICAL REMINDERS FOR THIS CONVERSATION:
- You are Magonia, the agricultural assistant
- ALWAYS use tools for data-related questions - NEVER rely on memory or previous knowledge
- Respond in {detected_language} to match the user's language
- Maintain a conversational, discussion-focused style
- Present complete field data when tools provide it - don't summarize
- Build on conversation history naturally
- Ask follow-up questions to encourage deeper agricultural discussions
- Show genuine interest in the farmer's specific situation and challenges
"""

            # Prepare conversation history
            conversation_context = ""
            if chat_history:
                try:
                    history_data = json.loads(chat_history)
                    conversation_parts = []
                    for msg in history_data:
                        if msg.get("role") == "user":
                            conversation_parts.append(f"User: {msg['content']}")
                        elif msg.get("role") == "assistant":
                            conversation_parts.append(f"Assistant: {msg['content']}")
                    conversation_context = "\n".join(conversation_parts)
                except:
                    # If parsing fails, start fresh
                    conversation_context = ""

            # Create the full prompt
            full_prompt = f"""{system_message}

{tool_reminder}

Previous conversation:
{conversation_context}

Current user question: {question}

Please respond appropriately using tools when needed and maintaining the conversational style."""

            # Get available tools
            tools = self._format_tools_for_gemini()

            print(f"Sending request to Gemini with {len(tools[0].function_declarations)} tools available")

            # Create model with tools
            model_with_tools = genai.GenerativeModel(
                'gemini-1.5-pro-latest',
                tools=tools
            )

            # Start chat session
            chat = model_with_tools.start_chat()

            # Send the message
            response = chat.send_message(
                full_prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.7,
                    max_output_tokens=2000
                )
            )

            # Check if the model wants to use tools
            if response.candidates[0].content.parts:
                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'function_call') and part.function_call:
                        # Execute the function call
                        function_call = part.function_call
                        tool_name = function_call.name
                        tool_args = dict(function_call.args)

                        print(f"Gemini wants to use tool: {tool_name} with args: {tool_args}")

                        # Execute the tool
                        tool_result = self._execute_tool(tool_name, tool_args, user_id, detected_language)

                        # Send the tool result back to Gemini
                        function_response = genai.protos.FunctionResponse(
                            name=tool_name,
                            response={"result": str(tool_result)}
                        )

                        # Continue the conversation with the tool result
                        final_response = chat.send_message(
                            genai.protos.Part(function_response=function_response),
                            generation_config=genai.types.GenerationConfig(
                                temperature=0.7,
                                max_output_tokens=2000
                            )
                        )

                        final_answer = final_response.text
                        break
                else:
                    # No function calls, use the direct response
                    final_answer = response.text
            else:
                # No content parts, handle error
                final_answer = "I apologize, but I encountered an issue processing your request. Please try again."

            print(f"Final response: {final_answer}")
            return final_answer

        except Exception as e:
            traceback.print_exc()
            error_msg = f"Error processing request with Gemini: {str(e)}"
            print(error_msg)
            return f"I apologize, but I encountered an error while processing your request. Please try again or rephrase your question."


# Example usage and testing functions
def test_gemini_direct():
    """Test the Gemini direct implementation."""
    gemini_tools = GeminiDirectTools()

    # Test questions
    test_questions = [
        "do i need to irrigate today?",
        "what fields need irrigation today?",
        "combien d'eau j'ai besoin pour l'irrigation aujourd'hui?",
        "show me all my parcels irrigation status"
    ]

    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"

    for question in test_questions:
        print(f"\n{'='*60}")
        print(f"Question: {question}")
        print(f"{'='*60}")

        try:
            response = gemini_tools.ask(question, user_id=user_id)
            print(f"Response: {response}")
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    test_gemini_direct()
