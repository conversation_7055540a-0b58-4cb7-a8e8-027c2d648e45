#!/usr/bin/env python3
"""
Test specific irrigation tool directly with Flask context
Testing check_today_active_irrigation_user tool specifically
"""

import sys
import os
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import Flask app and create context
    from magonia.main import create_application
    from magonia.seabex_api import SeabexAPI
    from flask import g
    
    # Import the specific tool we want to test
    from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
    
    print("✅ Successfully imported Flask app and irrigation tool")
except ImportError as e:
    print(f"❌ Failed to import components: {e}")
    sys.exit(1)

def setup_flask_context(app, user_id):
    """Set up Flask application context with SeabexAPI"""
    try:
        # Get credentials from environment
        client_id = os.getenv("SEABEX_CLIENT_ID")
        client_secret = os.getenv("SEABEX_CLIENT_SECRET")
        
        if not client_id or not client_secret:
            print("❌ Missing SEABEX_CLIENT_ID or SEABEX_CLIENT_SECRET in environment")
            return False
        
        # Create and configure SeabexAPI
        seabex_api = SeabexAPI(client_id, client_secret)
        seabex_api.set_scopes(["magonia-api"])
        seabex_api.set_user_id(user_id)
        seabex_api.authenticate()
        
        # Set up Flask context
        g.seabex_api = seabex_api
        
        print(f"✅ Flask context set up successfully for user: {user_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to set up Flask context: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_irrigation_tool_directly():
    """Test the irrigation tool directly with Flask context"""
    
    print("🚀 Testing check_today_active_irrigation_user Tool Directly")
    print("=" * 60)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Test data
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    
    # Create Flask app
    app = create_application()
    
    print(f"👤 User ID: {user_id}")
    print("=" * 60)
    
    # Run test within Flask application context
    with app.app_context():
        # Set up the Flask context with SeabexAPI
        if not setup_flask_context(app, user_id):
            print("❌ Failed to set up Flask context. Cannot proceed with real tools.")
            return
        
        print("\n🔍 Testing check_today_active_irrigation_user tool directly...")
        print("-" * 50)
        
        try:
            print("🔄 Calling check_today_active_irrigation_user()...")
            
            # Call the tool directly
            result = check_today_active_irrigation_user()
            
            print(f"✅ Tool executed successfully!")
            print(f"📝 Result type: {type(result)}")
            print(f"📝 Result: {result}")
            
            # Check if result contains irrigation data
            if result and "irrigation" in str(result).lower():
                print("🎯 SUCCESS: Tool returned irrigation data!")
            elif result and len(str(result)) > 10:
                print("🎯 SUCCESS: Tool returned substantial data!")
            else:
                print("⚠️  WARNING: Tool returned minimal or no data")
            
        except Exception as e:
            print(f"❌ Error calling tool: {str(e)}")
            print(f"🔧 Error type: {type(e).__name__}")
            
            # Print more detailed error info
            import traceback
            traceback.print_exc()
        
        print("-" * 50)
        
        # Test with different parameters
        print("\n🔍 Testing with empty string parameter...")
        try:
            result2 = check_today_active_irrigation_user("")
            print(f"✅ Tool with empty string executed!")
            print(f"📝 Result: {result2}")
        except Exception as e:
            print(f"❌ Error with empty string: {str(e)}")
        
        print("-" * 50)
        
        # Test with None parameter
        print("\n🔍 Testing with None parameter...")
        try:
            result3 = check_today_active_irrigation_user(None)
            print(f"✅ Tool with None executed!")
            print(f"📝 Result: {result3}")
        except Exception as e:
            print(f"❌ Error with None: {str(e)}")

def test_simple_mock_comparison():
    """Compare with mock tool to see the difference"""
    print("\n" + "=" * 60)
    print("🔄 COMPARISON: Testing Mock Tool")
    print("=" * 60)
    
    try:
        # Import mock tool
        from magonia.tools_local.mock_irrigation_tools import check_today_active_irrigation_user as mock_tool
        
        print("🔍 Testing mock tool...")
        mock_result = mock_tool()
        print(f"✅ Mock tool executed!")
        print(f"📝 Mock result: {mock_result}")
        
    except Exception as e:
        print(f"❌ Error with mock tool: {str(e)}")

if __name__ == "__main__":
    try:
        test_irrigation_tool_directly()
        test_simple_mock_comparison()
        print(f"\n🏁 Direct tool test completed.")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
