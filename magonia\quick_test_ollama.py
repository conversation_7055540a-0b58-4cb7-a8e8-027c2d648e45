#!/usr/bin/env python3
"""
Quick test for Ollama Devstral direct implementation
"""

import sys
import os
from datetime import datetime

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ollama_devstral_quick():
    """Quick test of Ollama Devstral direct implementation."""
    
    print("🚀 Quick Test: Ollama Devstral Direct")
    print("=" * 50)
    print(f"📅 Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    try:
        # Import the Ollama Devstral implementation
        from ollama_devstral_direct import OllamaDevstralDirectTools
        print("✅ Successfully imported OllamaDevstralDirectTools")
        
        # Initialize the tools
        print("\n🔧 Initializing Ollama Devstral tools...")
        ollama_tools = OllamaDevstralDirectTools(
            enable_translation=True,
            enable_context_analysis=True
        )
        print("✅ Ollama Devstral tools initialized successfully")
        
        # Test user ID (same as your other tests)
        user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
        
        # Test questions
        test_questions = [
            "do i need to irrigate today?",
            "what fields need irrigation today?",
            "combien d'eau j'ai besoin pour l'irrigation aujourd'hui?"
        ]
        
        print(f"\n👤 User ID: {user_id}")
        print(f"📝 Testing {len(test_questions)} questions...")
        print("=" * 50)
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔍 Test {i}: {question}")
            print("-" * 40)
            
            try:
                # Ask the question
                response = ollama_tools.ask(
                    question=question,
                    chat_history="",
                    user_id=user_id
                )
                
                print(f"✅ Response: {response}")
                print(f"📏 Response length: {len(response)} characters")
                
                # Check if response looks valid
                if response and len(response.strip()) > 10:
                    print(f"✅ Test {i} PASSED")
                else:
                    print(f"❌ Test {i} FAILED - Response too short")
                    
            except Exception as e:
                print(f"❌ Test {i} FAILED - Error: {str(e)}")
                import traceback
                traceback.print_exc()
        
        print("\n🏁 Quick test completed!")
        
    except ImportError as e:
        print(f"❌ Import Error: {str(e)}")
        print("\n💡 Make sure:")
        print("   1. Ollama is running (http://localhost:11434)")
        print("   2. Devstral model is available")
        print("   3. All dependencies are installed")
        
    except Exception as e:
        print(f"❌ Unexpected Error: {str(e)}")
        import traceback
        traceback.print_exc()

def check_ollama_status():
    """Check if Ollama is running and Devstral is available."""
    
    print("\n🔍 Checking Ollama Status...")
    print("-" * 30)
    
    try:
        import requests
        
        # Check if Ollama is running
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        
        if response.status_code == 200:
            print("✅ Ollama is running")
            
            # Check available models
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]
            
            print(f"📋 Available models: {len(models)}")
            for model_name in model_names:
                print(f"   • {model_name}")
            
            # Check if Devstral is available
            devstral_models = [name for name in model_names if "devstral" in name.lower()]
            if devstral_models:
                print(f"✅ Devstral found: {devstral_models}")
            else:
                print("⚠️ Devstral not found. You may need to pull it:")
                print("   ollama pull devstral:latest")
                
        else:
            print(f"❌ Ollama responded with status: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to Ollama")
        print("💡 Make sure Ollama is running:")
        print("   1. Start Ollama service")
        print("   2. Check http://localhost:11434")
        
    except Exception as e:
        print(f"❌ Error checking Ollama: {str(e)}")

if __name__ == "__main__":
    print("🌾 Ollama Devstral Quick Test")
    print("=" * 50)
    
    # First check Ollama status
    check_ollama_status()
    
    # Then run the test
    test_ollama_devstral_quick()
