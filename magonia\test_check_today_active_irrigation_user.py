import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Add the parent directory to import magonia modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from magonia.seabex_api import SeabexAPI

# Load environment variables
load_dotenv()

# Initialize the API client
client_id = os.getenv("SEABEX_CLIENT_ID")
client_secret = os.getenv("SEABEX_CLIENT_SECRET")
user_id = "f68381cd-a748-47bd-842c-701790b35e3c"  # Replace with your test user ID

# Create API instance
api = SeabexAPI(client_id, client_secret)
api.set_scopes(["magonia-api"])
api.set_user_id(user_id)
api.authenticate()

# Dates to test (7-day range)
today = datetime.now().date()
dates = [(today + timedelta(days=i)).strftime('%Y-%m-%d') for i in range(7)]

print("\n=== Testing check_highest_evapotranspiration ===")
for d in dates:
    print(f"\nTesting evapotranspiration on: {d}")
    try:
        result = api.tools().irrigations().call_tool(
            'check_highest_evapotranspiration',
            {
                'calculation_date': d
            }
        )
        print(f"Result: {result}")
    except Exception as e:
        print(f"Error on date {d}: {e}")
