#!/usr/bin/env python3
"""
Performance and functionality test for local Ollama Mistral implementation.
This script tests actual tool execution, response quality, and performance metrics.
"""

import os
import sys
import time
import json
from datetime import datetime, timedelta

# Add the magonia directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'magonia'))

try:
    from magonia.local import ask_ollama_mistral, OllamaMistralTools
    print("✅ Successfully imported local modules")
except ImportError as e:
    print(f"❌ Failed to import local modules: {e}")
    sys.exit(1)

class PerformanceTestSuite:
    """Performance and functionality test suite."""
    
    def __init__(self):
        self.test_user_id = "perf_test_user"
        self.results = []
        self.tools = OllamaMistralTools()
    
    def measure_response_time(self, test_name: str, question: str, expected_keywords: list = None):
        """Measure response time and quality for a question."""
        print(f"\n🔍 Testing: {test_name}")
        print(f"Question: {question}")
        
        start_time = time.time()
        try:
            response = ask_ollama_mistral(question, user_id=self.test_user_id)
            end_time = time.time()
            
            duration = end_time - start_time
            response_length = len(response) if response else 0
            
            # Check for expected keywords if provided
            keyword_score = 0
            if expected_keywords and response:
                found_keywords = [kw for kw in expected_keywords if kw.lower() in response.lower()]
                keyword_score = len(found_keywords) / len(expected_keywords) * 100
            
            result = {
                "test_name": test_name,
                "question": question,
                "response_time": duration,
                "response_length": response_length,
                "keyword_score": keyword_score,
                "success": response is not None and response_length > 0,
                "response_preview": response[:200] + "..." if response and len(response) > 200 else response,
                "timestamp": datetime.now().isoformat()
            }
            
            self.results.append(result)
            
            print(f"⏱️  Response time: {duration:.2f}s")
            print(f"📝 Response length: {response_length} chars")
            if expected_keywords:
                print(f"🎯 Keyword relevance: {keyword_score:.1f}%")
            print(f"📄 Preview: {result['response_preview']}")
            
            return result
            
        except Exception as e:
            print(f"❌ Error: {e}")
            result = {
                "test_name": test_name,
                "question": question,
                "response_time": -1,
                "response_length": 0,
                "keyword_score": 0,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
            self.results.append(result)
            return result
    
    def test_basic_agricultural_questions(self):
        """Test basic agricultural questions."""
        print("\n🌾 Testing Basic Agricultural Questions")
        print("=" * 50)
        
        test_cases = [
            {
                "name": "General Greeting",
                "question": "Hello, I'm a farmer and need help with irrigation",
                "keywords": ["magonia", "agricultural", "irrigation", "help", "farming"]
            },
            {
                "name": "Field Information Request",
                "question": "What fields do I have in my farm?",
                "keywords": ["field", "farm", "area", "list"]
            },
            {
                "name": "Current Time Request",
                "question": "What time is it now?",
                "keywords": ["time", "date", "current", "now"]
            },
            {
                "name": "Today's Irrigation",
                "question": "Which fields need irrigation today?",
                "keywords": ["irrigation", "today", "field", "need"]
            },
            {
                "name": "Specific Field Query",
                "question": "How much water does CHlewi field need today?",
                "keywords": ["chlewi", "water", "irrigation", "today", "mm"]
            }
        ]
        
        for test_case in test_cases:
            self.measure_response_time(
                test_case["name"],
                test_case["question"],
                test_case["keywords"]
            )
            time.sleep(1)  # Small delay between requests
    
    def test_multilingual_performance(self):
        """Test multilingual capabilities."""
        print("\n🌍 Testing Multilingual Performance")
        print("=" * 50)
        
        test_cases = [
            {
                "name": "French Question",
                "question": "Bonjour, combien d'eau faut-il pour mes champs aujourd'hui?",
                "keywords": ["bonjour", "eau", "champs", "irrigation"]
            },
            {
                "name": "Spanish Question", 
                "question": "Hola, ¿cuánta agua necesitan mis campos hoy?",
                "keywords": ["agua", "campos", "riego", "hoy"]
            },
            {
                "name": "English Technical",
                "question": "What is the evapotranspiration rate for my fields?",
                "keywords": ["evapotranspiration", "rate", "field"]
            }
        ]
        
        for test_case in test_cases:
            self.measure_response_time(
                test_case["name"],
                test_case["question"],
                test_case["keywords"]
            )
            time.sleep(1)
    
    def test_complex_queries(self):
        """Test complex agricultural queries."""
        print("\n🔬 Testing Complex Queries")
        print("=" * 50)
        
        test_cases = [
            {
                "name": "Multi-day Irrigation Planning",
                "question": "What are the irrigation needs for the next 7 days?",
                "keywords": ["irrigation", "days", "planning", "next", "week"]
            },
            {
                "name": "Water Volume Analysis",
                "question": "Which fields have the lowest soil water volume?",
                "keywords": ["lowest", "soil", "water", "volume", "field"]
            },
            {
                "name": "Future Irrigation Prediction",
                "question": "Calculate total irrigation volume needed for next 5 days",
                "keywords": ["calculate", "total", "irrigation", "volume", "days"]
            },
            {
                "name": "Field Comparison",
                "question": "Show me fields with highest water requirements",
                "keywords": ["highest", "water", "requirements", "field"]
            }
        ]
        
        for test_case in test_cases:
            self.measure_response_time(
                test_case["name"],
                test_case["question"],
                test_case["keywords"]
            )
            time.sleep(2)  # Longer delay for complex queries
    
    def test_conversation_continuity(self):
        """Test conversation flow and context."""
        print("\n💬 Testing Conversation Continuity")
        print("=" * 50)
        
        # Simulate a conversation
        conversation_history = ""
        
        questions = [
            "Hello, I'm a new farmer",
            "What fields do I have?", 
            "How much irrigation does the first field need?",
            "What about tomorrow?",
            "Thank you for the help"
        ]
        
        for i, question in enumerate(questions):
            result = self.measure_response_time(
                f"Conversation Step {i+1}",
                question,
                ["field", "irrigation", "water"] if i > 0 else ["hello", "farmer"]
            )
            
            # Update conversation history
            if result["success"]:
                conversation_history += f"User: {question}\nAssistant: {result['response_preview']}\n"
            
            time.sleep(1)
    
    def test_error_handling(self):
        """Test error handling and edge cases."""
        print("\n⚠️ Testing Error Handling")
        print("=" * 50)
        
        test_cases = [
            {
                "name": "Empty Question",
                "question": "",
                "keywords": []
            },
            {
                "name": "Non-Agricultural Question",
                "question": "What's the weather like on Mars?",
                "keywords": ["agriculture", "farming", "sorry"]
            },
            {
                "name": "Very Long Question",
                "question": "What " + "is " * 100 + "the irrigation status?",
                "keywords": ["irrigation"]
            },
            {
                "name": "Special Characters",
                "question": "How much water for field #@$%^&*()?",
                "keywords": ["water", "field"]
            }
        ]
        
        for test_case in test_cases:
            self.measure_response_time(
                test_case["name"],
                test_case["question"],
                test_case["keywords"]
            )
            time.sleep(1)
    
    def generate_performance_report(self):
        """Generate a comprehensive performance report."""
        print("\n📊 Performance Analysis")
        print("=" * 50)
        
        if not self.results:
            print("No test results to analyze")
            return
        
        successful_tests = [r for r in self.results if r["success"]]
        failed_tests = [r for r in self.results if not r["success"]]
        
        if successful_tests:
            response_times = [r["response_time"] for r in successful_tests]
            response_lengths = [r["response_length"] for r in successful_tests]
            keyword_scores = [r["keyword_score"] for r in successful_tests if r["keyword_score"] > 0]
            
            print(f"✅ Successful tests: {len(successful_tests)}/{len(self.results)}")
            print(f"⏱️  Average response time: {sum(response_times)/len(response_times):.2f}s")
            print(f"⚡ Fastest response: {min(response_times):.2f}s")
            print(f"🐌 Slowest response: {max(response_times):.2f}s")
            print(f"📝 Average response length: {sum(response_lengths)/len(response_lengths):.0f} chars")
            
            if keyword_scores:
                print(f"🎯 Average keyword relevance: {sum(keyword_scores)/len(keyword_scores):.1f}%")
        
        if failed_tests:
            print(f"\n❌ Failed tests: {len(failed_tests)}")
            for test in failed_tests:
                print(f"  - {test['test_name']}: {test.get('error', 'Unknown error')}")
        
        # Save detailed results
        self.save_performance_results()
    
    def save_performance_results(self):
        """Save performance results to file."""
        try:
            report = {
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": len(self.results),
                    "successful_tests": len([r for r in self.results if r["success"]]),
                    "failed_tests": len([r for r in self.results if not r["success"]]),
                    "average_response_time": sum([r["response_time"] for r in self.results if r["success"]]) / len([r for r in self.results if r["success"]]) if any(r["success"] for r in self.results) else 0
                },
                "detailed_results": self.results
            }
            
            filename = f"local_ollama_performance_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, "w") as f:
                json.dump(report, f, indent=2)
            
            print(f"\n💾 Performance results saved to: {filename}")
        except Exception as e:
            print(f"⚠️ Could not save performance results: {e}")
    
    def run_all_performance_tests(self):
        """Run all performance tests."""
        print("🚀 Starting Local Ollama Performance Test Suite")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run test suites
        self.test_basic_agricultural_questions()
        self.test_multilingual_performance()
        self.test_complex_queries()
        self.test_conversation_continuity()
        self.test_error_handling()
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        print(f"\n⏱️ Total test duration: {total_duration:.2f} seconds")
        
        # Generate report
        self.generate_performance_report()
        
        return len([r for r in self.results if r["success"]]) > 0

def main():
    """Main performance test execution."""
    test_suite = PerformanceTestSuite()
    success = test_suite.run_all_performance_tests()
    
    if success:
        print("\n🎉 Performance testing completed!")
        return 0
    else:
        print("\n⚠️ Performance testing encountered issues.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
