
# Initialize Redis client
import os
from redis import Redis

redis_url = os.getenv("REDIS_URL")

# Initialize Redis client only if URL is provided
if redis_url:
    try:
        redis_client = Redis.from_url(redis_url)
        # Test connection
        redis_client.ping()
        print("✅ Redis client initialized successfully")
    except Exception as e:
        print(f"⚠️ Redis connection failed: {e}")
        redis_client = None
else:
    print("⚠️ REDIS_URL not set, Redis client disabled")
    redis_client = None