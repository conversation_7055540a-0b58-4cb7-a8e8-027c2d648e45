from datetime import datetime
import traceback
from typing import Optional, Union
from flask import g
from langchain_core.tools import tool

@tool
def check_today_active_irrigation_user(tool_input: Optional[str] = None):
    """
    Get a list of all fields that need irrigation today, including recommended volumes.

    USE THIS TOOL WHEN:
    - The user asks if they need to irrigate today
    - The user asks about today's irrigation recommendations
    - The user wants to know how much water to apply today
    - The user asks about current irrigation needs
    - The user wants to know which fields need irrigation today

    DO NOT USE THIS TOOL WHEN:
    - The user asks about irrigation for a specific future date (use check_irrigation_needs_between_period)
    - The user asks about irrigation for a specific field (use field-specific tools)
    - The user asks about irrigation for a period of days (use check_irrigation_need_for_x_days)
    - The user is asking about soil moisture, not irrigation (use soil moisture tools)

    EXAMPLE QUERIES:
    - "Do I need to irrigate today?"
    - "What are today's irrigation recommendations?"
    - "How much water should I apply today?"
    - "Is there any irrigation needed for today?"

    Args:
        tool_input (str, optional): Ignored parameter, exists for compatibility.

    Returns:
        str: A summary of active irrigation recommendations including total volume and details by area,
             or a message indicating no irrigation is needed or that there was an issue with data retrieval.
    """

    try:
        response = g.seabex_api.tools().irrigations().call_tool(
            'check_today_active_irrigation_user'
        )

        if isinstance(response, dict) and 'error' in response:
            error_message = response['error']
            return {"message": f"Error: {error_message}"}

        if isinstance(response, dict) and response.get("count", 0) > 0:
            total_volume = response["total_irrigation_volume"]
            areas = response["areas"]


            if isinstance(areas, list):
                area_info = []


                # Create a more detailed list of fields that need irrigation
                fields_needing_irrigation = []

                for area in areas:
                    if isinstance(area, dict):
                        area_name = area.get('area_name', 'Unknown')
                        irrigation_volume = area.get('irrigation_volume', 0)

                        # Only include fields that actually need irrigation (volume > 0)
                        if float(irrigation_volume) > 0:
                            fields_needing_irrigation.append({
                                'name': area_name,
                                'volume': irrigation_volume
                            })
                            area_info.append(f"{area_name}: {irrigation_volume} mm")
                    else:
                        return "Unexpected format in irrigation area data."

                # If no fields need irrigation
                if not fields_needing_irrigation:
                    return "None of your fields need irrigation today."

                area_details = "\n".join(area_info)

                # Create a more informative response
                response = (
                    f"The total irrigation recommendation for today is {total_volume:.2f} mm.\n"
                    f"Fields that need irrigation today:\n{area_details}"
                )

                # Add a summary of how many fields need irrigation
                response += f"\n\nIn total, {len(fields_needing_irrigation)} field(s) need irrigation today."

                return response
            else:
                return "Irrigation areas data is not in the expected format."

        else:
            return "No fields need irrigation today."

    except Exception as e:
        # Print the exact error for better debugging
        print(f"Error retrieving irrigation data: {e}")
        traceback.print_exc()
        return (
            "I'm sorry, I couldn't retrieve the active irrigation recommendation at the moment. "
            "Please try again later."
        )
