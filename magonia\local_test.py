#!/usr/bin/env python3
"""
Local test script for Ollama Devstral with real tools and Flask context
Similar to send_test.py but uses local Ollama instead of API calls
Works with real irrigation data through proper Flask context setup
"""

import os
import sys
import time
import traceback
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import Flask app and create context
    from magonia.main import create_application
    from magonia.seabex_api import SeabexAPI
    from flask import g
    
    # Import the local Ollama function
    from magonia.local import ask_ollama_devstral
    
    print("✅ Successfully imported Flask app and Ollama Devstral")
except ImportError as e:
    print(f"❌ Failed to import components: {e}")
    sys.exit(1)

def setup_flask_context(app, user_id):
    """Set up Flask application context with SeabexAPI for real tools"""
    try:
        # Get credentials from environment
        client_id = os.getenv("SEABEX_CLIENT_ID")
        client_secret = os.getenv("SEABEX_CLIENT_SECRET")
        
        if not client_id or not client_secret:
            print("❌ Missing SEABEX_CLIENT_ID or SEABEX_CLIENT_SECRET in environment")
            return False
        
        # Create and configure SeabexAPI
        seabex_api = SeabexAPI(client_id, client_secret)
        seabex_api.set_scopes(["magonia-api"])
        seabex_api.set_user_id(user_id)
        seabex_api.authenticate()
        
        # Set up Flask context
        g.seabex_api = seabex_api
        
        print(f"✅ Flask context set up successfully for user: {user_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to set up Flask context: {e}")
        import traceback
        traceback.print_exc()
        return False

def send_prompt_local(prompt, session_id="session123", user_id="f68381cd-a748-47bd-842c-701790b35e3c", chat_history=""):
    """
    Send a prompt to local Ollama Devstral with real tools and Flask context
    Similar to send_prompt in send_test.py but uses local Ollama instead of API
    """
    try:
        print(f"🔄 Processing with Ollama Devstral (Real Tools + Flask Context)...")
        
        # Call the local Ollama Devstral function with real tools
        response = ask_ollama_devstral(
            question=prompt,
            chat_history=chat_history,
            user_id=user_id
        )
        
        print(f"✅ Response received from Ollama Devstral")
        print(f"📝 Response: {response}")
        
        return response
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        print(f"🔧 Error type: {type(e).__name__}")
        
        # Print more detailed error info
        traceback.print_exc()
        
        return f"Error: {str(e)}"

def main():
    """Main function to test local Ollama Devstral with real tools"""
    
    print("🚀 Local Ollama Devstral Test with Real Tools")
    print("=" * 70)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🤖 Model: Ollama Devstral with Real Tools")
    print("=" * 70)
    
    # Test configuration (same as send_test.py)
    session_id = "session123"
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    chat_history = ""
    
    print(f"👤 User ID: {user_id}")
    print(f"🔗 Session ID: {session_id}")
    print("=" * 70)
    
    # Prompts to test (same as send_test.py but focused on irrigation)
    prompts = [
        "do i need to irrigate today?",
        "what fields need irrigation today?",
        "show me today's irrigation status",
        "which field needs the most irrigation?",
        "what are my fields?",
        "est ce que je dois irriguer aujourd'hui ?",
        "quelles sont mes parcelles ?",
        "combien je dois irriguer aujourd'hui ?",
        "affiche moi le nom du champ qui a besoin de plus d irrigation ?",
        "يجب أن أسقي اليوم؟"
    ]
    
    # Create Flask app
    app = create_application()
    
    print(f"🧪 Total questions to test: {len(prompts)}")
    print("=" * 70)
    
    results = []
    total_start_time = time.time()
    
    # Run tests within Flask application context
    with app.app_context():
        # Set up the Flask context with SeabexAPI
        if not setup_flask_context(app, user_id):
            print("❌ Failed to set up Flask context. Cannot proceed with real tools.")
            return
        
        # Process each prompt
        for i, prompt in enumerate(prompts, 1):
            print(f"\n🔍 Test {i}/{len(prompts)}")
            print("-" * 50)
            print(f"PROMPT: {prompt}")
            
            try:
                start_time = time.time()
                
                # Send prompt to local Ollama Devstral
                response = send_prompt_local(
                    prompt=prompt,
                    session_id=session_id,
                    user_id=user_id,
                    chat_history=chat_history
                )
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                # Format execution time
                if execution_time < 1:
                    execution_message = f"{execution_time * 1000:.2f} ms"
                elif execution_time < 60:
                    execution_message = f"{execution_time:.2f} seconds"
                else:
                    minutes = execution_time // 60
                    seconds = execution_time % 60
                    execution_message = f"{int(minutes)} minute(s) and {seconds:.2f} seconds"
                
                print(f"⏱️  Execution time: {execution_message}")
                
                # Store result
                results.append({
                    'prompt': prompt,
                    'response': response,
                    'execution_time': execution_time,
                    'status': 'success'
                })
                
                # Update chat history for context (optional)
                # chat_history += f"Human: {prompt}\nAI: {response}\n"
                
            except Exception as e:
                print(f"❌ Error processing prompt: {str(e)}")
                
                results.append({
                    'prompt': prompt,
                    'response': f"Error: {str(e)}",
                    'execution_time': 0,
                    'status': 'error'
                })
            
            print("-" * 50)
    
    # Calculate total execution time
    total_end_time = time.time()
    total_execution_time = total_end_time - total_start_time
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    successful_tests = [r for r in results if r['status'] == 'success']
    failed_tests = [r for r in results if r['status'] == 'error']
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
        print(f"⏱️  Average execution time: {avg_time:.2f} seconds")
        
        fastest = min(successful_tests, key=lambda x: x['execution_time'])
        slowest = max(successful_tests, key=lambda x: x['execution_time'])
        print(f"🚀 Fastest response: {fastest['execution_time']:.2f}s - '{fastest['prompt'][:30]}...'")
        print(f"🐌 Slowest response: {slowest['execution_time']:.2f}s - '{slowest['prompt'][:30]}...'")
    
    # Format total execution time
    if total_execution_time < 1:
        total_execution_message = f"{total_execution_time * 1000:.2f} ms"
    elif total_execution_time < 60:
        total_execution_message = f"{total_execution_time:.2f} seconds"
    else:
        minutes = total_execution_time // 60
        seconds = total_execution_time % 60
        total_execution_message = f"{int(minutes)} minute(s) and {seconds:.2f} seconds"
    
    print(f"🏁 Total execution time: {total_execution_message}")
    
    if failed_tests:
        print("\n🔧 Failed Tests:")
        for test in failed_tests:
            print(f"   • {test['prompt']}: {test['response']}")
    
    print("\n🎯 Key Question Analysis:")
    target_question = "do i need to irrigate today?"
    target_result = next((r for r in results if r['prompt'] == target_question), None)
    
    if target_result:
        if target_result['status'] == 'success':
            print(f"✅ Target question answered successfully!")
            print(f"📝 Answer: {target_result['response'][:200]}...")
            print(f"⏱️  Time: {target_result['execution_time']:.2f} seconds")
        else:
            print(f"❌ Target question failed: {target_result['response']}")
    
    print(f"\n🏁 Local Ollama Devstral test completed with real tools!")
    return results

if __name__ == "__main__":
    try:
        results = main()
        print(f"\n✨ Test completed successfully. Check results above.")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
