#!/usr/bin/env python3
"""
Test script for the updated local.py with Devstral model.
This tests the real tools (not mock) with actual API calls.
"""

import os
import time
import traceback
from dotenv import load_dotenv

# Import the local Ollama implementation
import sys
import os

# Add parent directory to path so magonia module can be found
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

# Change to parent directory to ensure proper module resolution
os.chdir(parent_dir)

from magonia.local import ask_ollama_devstral

load_dotenv()

# Get credentials from environment variables (like send_test.py)
AUTH_TOKEN = os.getenv('AUTH_TOKEN')
DEFAULT_USER_ID = "f68381cd-a748-47bd-842c-701790b35e3c"  # From send_test.py
DEFAULT_SESSION_ID = "session123"  # From send_test.py

def test_devstral_prompt(prompt: str, user_id: str = DEFAULT_USER_ID, session_id: str = DEFAULT_SESSION_ID):
    """Test a single prompt with Devstral"""
    try:
        print(f"\n{'='*80}")
        print(f"🧪 Testing: {prompt}")
        print(f"{'='*80}")
        print(f"🔄 Processing prompt with Ollama Devstral...")
        print(f"📝 Session ID: {session_id}")
        print(f"👤 User ID: {user_id}")
        
        start_time = time.time()
        
        # Call the local Ollama implementation with real tools
        response = ask_ollama_devstral(
            question=prompt,
            user_id=user_id,
            chat_history=""
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"\n✅ Response received in {execution_time:.2f} seconds:")
        print(f"📋 Response: {response}")
        
        return True, response, execution_time
        
    except Exception as e:
        print(f"\n❌ Error testing prompt: {str(e)}")
        print(f"🔍 Traceback: {traceback.format_exc()}")
        return False, str(e), 0

def main():
    """Run comprehensive tests for Devstral"""
    print("🚀 Starting Devstral Testing Session")
    print(f"🔧 Using User ID: {DEFAULT_USER_ID}")
    print(f"🔧 Using Session ID: {DEFAULT_SESSION_ID}")
    
    # Test prompts - start with simple ones to verify the model is working
    test_prompts = [
        # Basic functionality tests
        "Hello, can you help me with irrigation?",
        "What is irrigation?",
        "Show me my current irrigation status",
        
        # Tool-specific tests
        "Check my fields that need irrigation today",
        "What are my field details?",
        "Show me soil water volume for my fields",
        
        # Multi-language tests
        "Bonjour, pouvez-vous m'aider avec l'irrigation?",
        "¿Puedes ayudarme con el riego?",
        
        # Complex queries
        "Which of my fields will need irrigation in the next 3 days?",
        "Calculate total water consumption for next 5 days",
    ]
    
    successful_tests = 0
    total_tests = len(test_prompts)
    total_time = 0
    
    print(f"\n📊 Running {total_tests} test prompts...")
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n🔄 Test {i}/{total_tests}")
        success, response, execution_time = test_devstral_prompt(prompt)
        
        if success:
            successful_tests += 1
        
        total_time += execution_time
        
        # Add a small delay between tests to avoid overwhelming the system
        time.sleep(1)
    
    # Print summary
    print(f"\n{'='*80}")
    print(f"📊 TEST SUMMARY")
    print(f"{'='*80}")
    print(f"✅ Successful tests: {successful_tests}/{total_tests}")
    print(f"❌ Failed tests: {total_tests - successful_tests}/{total_tests}")
    print(f"⏱️  Total execution time: {total_time:.2f} seconds")
    print(f"⏱️  Average time per test: {total_time/total_tests:.2f} seconds")
    print(f"🎯 Success rate: {(successful_tests/total_tests)*100:.1f}%")
    
    if successful_tests == total_tests:
        print(f"🎉 All tests passed! Devstral is working perfectly!")
    elif successful_tests > total_tests * 0.8:
        print(f"✅ Most tests passed! Devstral is working well with minor issues.")
    else:
        print(f"⚠️  Many tests failed. Please check the Devstral configuration.")

if __name__ == "__main__":
    main()
