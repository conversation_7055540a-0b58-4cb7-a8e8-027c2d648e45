#!/usr/bin/env python3
"""
Test script for real irrigation tools with Devstral using proper Flask context
Testing the specific question: "do i need to irrigate today?"
"""

import sys
import os
import time
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import Flask app and create context
    from magonia.main import create_application
    from magonia.seabex_api import SeabexAPI
    from flask import g
    
    print("✅ Successfully imported Flask app components")
except ImportError as e:
    print(f"❌ Failed to import Flask components: {e}")
    sys.exit(1)

try:
    from magonia.local import ask_ollama_devstral
    print("✅ Successfully imported ask_ollama_devstral from local.py")
except ImportError as e:
    print(f"❌ Failed to import ask_ollama_devstral: {e}")
    sys.exit(1)

def setup_flask_context(app, user_id):
    """Set up Flask application context with SeabexAPI"""
    try:
        # Get credentials from environment
        client_id = os.getenv("SEABEX_CLIENT_ID")
        client_secret = os.getenv("SEABEX_CLIENT_SECRET")
        
        if not client_id or not client_secret:
            print("❌ Missing SEABEX_CLIENT_ID or SEABEX_CLIENT_SECRET in environment")
            return False
        
        # Create and configure SeabexAPI
        seabex_api = SeabexAPI(client_id, client_secret)
        seabex_api.set_scopes(["magonia-api"])
        seabex_api.set_user_id(user_id)
        seabex_api.authenticate()
        
        # Set up Flask context
        g.seabex_api = seabex_api
        
        print(f"✅ Flask context set up successfully for user: {user_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to set up Flask context: {e}")
        return False

def test_irrigation_today_with_context():
    """Test the specific question about irrigation needs today with proper Flask context"""
    
    print("🚀 Testing Real Irrigation Tools with Devstral + Flask Context")
    print("=" * 70)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Target Question: 'do i need to irrigate today?'")
    print("=" * 70)
    
    # Test data
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    
    # Create Flask app
    app = create_application()
    
    # Test questions about irrigation today
    test_questions = [
        "do i need to irrigate today?",
        "what fields need irrigation today?",
        "show me today's irrigation status"
    ]
    
    print(f"👤 User ID: {user_id}")
    print(f"🧪 Total questions to test: {len(test_questions)}")
    print("=" * 70)
    
    results = []
    
    # Run tests within Flask application context
    with app.app_context():
        # Set up the Flask context with SeabexAPI
        if not setup_flask_context(app, user_id):
            print("❌ Failed to set up Flask context. Cannot proceed with real tools.")
            return []
        
        for i, question in enumerate(test_questions, 1):
            print(f"\n🔍 Test {i}/{len(test_questions)}")
            print("-" * 50)
            print(f"QUESTION: {question}")
            
            try:
                start_time = time.time()
                print(f"🔄 Processing with Devstral (Real Tools + Flask Context)...")
                
                # Call the real Devstral function with real tools and Flask context
                response = ask_ollama_devstral(
                    question=question,
                    chat_history="",
                    user_id=user_id
                )
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                print(f"✅ Response received")
                print(f"📝 Response: {response}")
                print(f"⏱️  Execution time: {execution_time:.2f} seconds")
                
                results.append({
                    'question': question,
                    'response': response,
                    'execution_time': execution_time,
                    'status': 'success'
                })
                
            except Exception as e:
                print(f"❌ Error: {str(e)}")
                print(f"🔧 Error type: {type(e).__name__}")
                
                # Print more detailed error info
                import traceback
                traceback.print_exc()
                
                results.append({
                    'question': question,
                    'response': f"Error: {str(e)}",
                    'execution_time': 0,
                    'status': 'error'
                })
            
            print("-" * 50)
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    successful_tests = [r for r in results if r['status'] == 'success']
    failed_tests = [r for r in results if r['status'] == 'error']
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
        print(f"⏱️  Average execution time: {avg_time:.2f} seconds")
    
    if failed_tests:
        print("\n🔧 Failed Tests:")
        for test in failed_tests:
            print(f"   • {test['question']}: {test['response']}")
    
    print("\n🎯 Key Question Analysis:")
    target_question = "do i need to irrigate today?"
    target_result = next((r for r in results if r['question'] == target_question), None)
    
    if target_result:
        if target_result['status'] == 'success':
            print(f"✅ Target question answered successfully!")
            print(f"📝 Answer: {target_result['response'][:200]}...")
            print(f"⏱️  Time: {target_result['execution_time']:.2f} seconds")
        else:
            print(f"❌ Target question failed: {target_result['response']}")
    
    return results

if __name__ == "__main__":
    try:
        results = test_irrigation_today_with_context()
        print(f"\n🏁 Test completed with Flask context. Check results above.")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
