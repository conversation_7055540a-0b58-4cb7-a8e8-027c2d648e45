#!/usr/bin/env python3
"""
Hybrid local test script that combines real tools with Ollama Devstral
Uses real tools directly when Devstral tool calling fails
Works with real irrigation data through proper Flask context setup
"""

import os
import sys
import time
import traceback
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables first
load_dotenv()

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Import Flask app and create context
    from magonia.main import create_application
    from magonia.seabex_api import SeabexAPI
    from flask import g
    
    # Import the local Ollama function
    from magonia.local import ask_ollama_devstral
    
    # Import real tools for direct calling
    from magonia.tools.check_today_active_irrigation_user import check_today_active_irrigation_user
    from magonia.tools.get_all_user_areas_with_children import get_all_user_areas_with_children
    from magonia.tools.check_highest_irrigation_requirement import check_highest_irrigation_requirement
    from magonia.tools.get_current_irrigation_status import get_current_irrigation_status
    
    print("✅ Successfully imported Flask app, Ollama Devstral, and real tools")
except ImportError as e:
    print(f"❌ Failed to import components: {e}")
    sys.exit(1)

def setup_flask_context(app, user_id):
    """Set up Flask application context with SeabexAPI for real tools"""
    try:
        # Get credentials from environment
        client_id = os.getenv("SEABEX_CLIENT_ID")
        client_secret = os.getenv("SEABEX_CLIENT_SECRET")
        
        if not client_id or not client_secret:
            print("❌ Missing SEABEX_CLIENT_ID or SEABEX_CLIENT_SECRET in environment")
            return False
        
        # Create and configure SeabexAPI
        seabex_api = SeabexAPI(client_id, client_secret)
        seabex_api.set_scopes(["magonia-api"])
        seabex_api.set_user_id(user_id)
        seabex_api.authenticate()
        
        # Set up Flask context
        g.seabex_api = seabex_api
        
        print(f"✅ Flask context set up successfully for user: {user_id}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to set up Flask context: {e}")
        return False

def call_real_tool_directly(question, user_id):
    """Call real tools directly based on question content"""
    question_lower = question.lower()
    
    try:
        if "irrigate today" in question_lower or "irrigation today" in question_lower:
            print("🔧 Using real tool: check_today_active_irrigation_user")
            result = check_today_active_irrigation_user("")
            return f"🌾 **IRRIGATION STATUS FOR TODAY** 🌾\n\n{result}"
            
        elif "my fields" in question_lower or "mes parcelles" in question_lower:
            print("🔧 Using real tool: get_all_user_areas_with_children")
            result = get_all_user_areas_with_children("")
            return f"🏞️ **YOUR FIELDS** 🏞️\n\n{result}"
            
        elif "most irrigation" in question_lower or "plus d irrigation" in question_lower:
            print("🔧 Using real tool: check_highest_irrigation_requirement")
            result = check_highest_irrigation_requirement("")
            return f"💧 **HIGHEST IRRIGATION REQUIREMENT** 💧\n\n{result}"
            
        elif "irrigation status" in question_lower:
            print("🔧 Using real tool: get_current_irrigation_status")
            result = get_current_irrigation_status("")
            return f"📊 **CURRENT IRRIGATION STATUS** 📊\n\n{result}"
            
        else:
            return None  # No direct tool match
            
    except Exception as e:
        print(f"❌ Error calling real tool: {e}")
        return f"Error getting real data: {str(e)}"

def send_prompt_hybrid(prompt, session_id="session123", user_id="f68381cd-a748-47bd-842c-701790b35e3c", chat_history=""):
    """
    Hybrid approach: Try Ollama first, fallback to real tools if needed
    """
    try:
        print(f"🔄 Step 1: Trying Ollama Devstral...")
        
        # Try Ollama Devstral first
        start_ollama = time.time()
        ollama_response = ask_ollama_devstral(
            question=prompt,
            chat_history=chat_history,
            user_id=user_id
        )
        end_ollama = time.time()
        ollama_time = end_ollama - start_ollama
        
        print(f"⏱️  Ollama time: {ollama_time:.2f}s")
        
        # Check if Ollama gave a meaningful response
        if ollama_response and len(ollama_response.strip()) > 10 and "sorry" not in ollama_response.lower():
            print(f"✅ Ollama provided good response")
            return ollama_response, "ollama", ollama_time
        else:
            print(f"⚠️  Ollama response insufficient: '{ollama_response[:50]}...'")
            
            # Try real tools directly
            print(f"🔄 Step 2: Trying real tools directly...")
            start_real = time.time()
            real_response = call_real_tool_directly(prompt, user_id)
            end_real = time.time()
            real_time = end_real - start_real
            
            print(f"⏱️  Real tool time: {real_time:.2f}s")
            
            if real_response:
                print(f"✅ Real tool provided response")
                return real_response, "real_tool", real_time
            else:
                print(f"⚠️  No matching real tool found")
                return ollama_response or "No response available", "fallback", ollama_time
        
    except Exception as e:
        print(f"❌ Error in hybrid approach: {str(e)}")
        traceback.print_exc()
        return f"Error: {str(e)}", "error", 0

def main():
    """Main function to test hybrid approach"""
    
    print("🚀 Hybrid Local Test: Ollama Devstral + Real Tools")
    print("=" * 70)
    print(f"📅 Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🤖 Strategy: Ollama first, real tools fallback")
    print("=" * 70)
    
    # Test configuration
    session_id = "session123"
    user_id = "f68381cd-a748-47bd-842c-701790b35e3c"
    chat_history = ""
    
    print(f"👤 User ID: {user_id}")
    print(f"🔗 Session ID: {session_id}")
    print("=" * 70)
    
    # Focused test prompts
    prompts = [
        "do i need to irrigate today?",
        "what are my fields?",
        "which field needs the most irrigation?",
        "show me today's irrigation status",
        "est ce que je dois irriguer aujourd'hui ?",
        "quelles sont mes parcelles ?",
        "affiche moi le nom du champ qui a besoin de plus d irrigation ?"
    ]
    
    # Create Flask app
    app = create_application()
    
    print(f"🧪 Total questions to test: {len(prompts)}")
    print("=" * 70)
    
    results = []
    total_start_time = time.time()
    
    # Run tests within Flask application context
    with app.app_context():
        # Set up the Flask context with SeabexAPI
        if not setup_flask_context(app, user_id):
            print("❌ Failed to set up Flask context. Cannot proceed with real tools.")
            return
        
        # Process each prompt
        for i, prompt in enumerate(prompts, 1):
            print(f"\n🔍 Test {i}/{len(prompts)}")
            print("-" * 50)
            print(f"PROMPT: {prompt}")
            
            try:
                start_time = time.time()
                
                # Use hybrid approach
                response, method, method_time = send_prompt_hybrid(
                    prompt=prompt,
                    session_id=session_id,
                    user_id=user_id,
                    chat_history=chat_history
                )
                
                end_time = time.time()
                total_time = end_time - start_time
                
                print(f"📝 Response ({method}): {response[:200]}...")
                print(f"⏱️  Total time: {total_time:.2f}s (method: {method_time:.2f}s)")
                
                # Store result
                results.append({
                    'prompt': prompt,
                    'response': response,
                    'method': method,
                    'execution_time': total_time,
                    'method_time': method_time,
                    'status': 'success'
                })
                
            except Exception as e:
                print(f"❌ Error processing prompt: {str(e)}")
                
                results.append({
                    'prompt': prompt,
                    'response': f"Error: {str(e)}",
                    'method': 'error',
                    'execution_time': 0,
                    'method_time': 0,
                    'status': 'error'
                })
            
            print("-" * 50)
    
    # Calculate total execution time
    total_end_time = time.time()
    total_execution_time = total_end_time - total_start_time
    
    # Summary
    print("\n" + "=" * 70)
    print("📊 HYBRID TEST SUMMARY")
    print("=" * 70)
    
    successful_tests = [r for r in results if r['status'] == 'success']
    failed_tests = [r for r in results if r['status'] == 'error']
    
    print(f"✅ Successful tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed tests: {len(failed_tests)}/{len(results)}")
    
    # Method breakdown
    method_counts = {}
    for result in successful_tests:
        method = result['method']
        method_counts[method] = method_counts.get(method, 0) + 1
    
    print(f"\n📈 Method Usage:")
    for method, count in method_counts.items():
        print(f"   • {method}: {count} times")
    
    if successful_tests:
        avg_time = sum(r['execution_time'] for r in successful_tests) / len(successful_tests)
        print(f"⏱️  Average execution time: {avg_time:.2f} seconds")
    
    print(f"🏁 Total execution time: {total_execution_time:.2f} seconds")
    
    # Show key results
    print("\n🎯 Key Results:")
    target_question = "do i need to irrigate today?"
    target_result = next((r for r in results if r['prompt'] == target_question), None)
    
    if target_result and target_result['status'] == 'success':
        print(f"✅ '{target_question}'")
        print(f"   Method: {target_result['method']}")
        print(f"   Time: {target_result['execution_time']:.2f}s")
        print(f"   Answer: {target_result['response'][:100]}...")
    
    return results

if __name__ == "__main__":
    try:
        results = main()
        print(f"\n✨ Hybrid test completed successfully!")
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
