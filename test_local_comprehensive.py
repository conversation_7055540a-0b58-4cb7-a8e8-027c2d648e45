#!/usr/bin/env python3
"""
Comprehensive test suite for the local Ollama Mistral implementation.
This script tests all major functionality of the local.py file.
"""

import os
import sys
import time
import json
from datetime import datetime

# Add the magonia directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'magonia'))

try:
    from magonia.local import ask_ollama_mistral, OllamaMistralTools, OllamaClient
    print("✅ Successfully imported local modules")
except ImportError as e:
    print(f"❌ Failed to import local modules: {e}")
    sys.exit(1)

class LocalOllamaTestSuite:
    """Comprehensive test suite for local Ollama implementation."""
    
    def __init__(self):
        self.test_user_id = "test_user_123"
        self.passed_tests = 0
        self.failed_tests = 0
        self.test_results = []
    
    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test results."""
        status = "✅ PASS" if passed else "❌ FAIL"
        result = f"{status}: {test_name}"
        if message:
            result += f" - {message}"
        
        print(result)
        self.test_results.append({
            "name": test_name,
            "passed": passed,
            "message": message,
            "timestamp": datetime.now().isoformat()
        })
        
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
    
    def test_ollama_connection(self):
        """Test 1: Ollama service connectivity."""
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get("models", [])
                self.log_test("Ollama Connection", True, f"Found {len(models)} models")
                return True
            else:
                self.log_test("Ollama Connection", False, f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Ollama Connection", False, str(e))
            return False
    
    def test_mistral_availability(self):
        """Test 2: Mistral model availability."""
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=10)
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model['name'] for model in models]
                
                mistral_available = any('mistral' in name.lower() for name in model_names)
                self.log_test("Mistral Model Available", mistral_available, 
                            f"Models: {model_names}" if mistral_available else "Run: ollama pull mistral")
                return mistral_available
            else:
                self.log_test("Mistral Model Available", False, "Cannot check models")
                return False
        except Exception as e:
            self.log_test("Mistral Model Available", False, str(e))
            return False
    
    def test_ollama_client_initialization(self):
        """Test 3: OllamaClient initialization."""
        try:
            client = OllamaClient()
            self.log_test("OllamaClient Init", True, f"Base URL: {client.base_url}, Model: {client.model}")
            return True
        except Exception as e:
            self.log_test("OllamaClient Init", False, str(e))
            return False
    
    def test_tools_registration(self):
        """Test 4: Tools registration and count."""
        try:
            tools = OllamaMistralTools()
            tool_count = len(tools.tools)
            expected_min_tools = 20  # We expect at least 20 tools
            
            success = tool_count >= expected_min_tools
            self.log_test("Tools Registration", success, f"Registered {tool_count} tools")
            
            # Test specific important tools
            important_tools = [
                "check_irrigation_user_data",
                "check_today_active_irrigation_user",
                "get_all_user_areas_with_children",
                "lookup_document_tool",
                "get_current_time_date"
            ]
            
            for tool_name in important_tools:
                if tool_name in tools.tools:
                    self.log_test(f"Tool: {tool_name}", True, "Available")
                else:
                    self.log_test(f"Tool: {tool_name}", False, "Missing")
            
            return success
        except Exception as e:
            self.log_test("Tools Registration", False, str(e))
            return False
    
    def test_language_detection(self):
        """Test 5: Language detection functionality."""
        try:
            tools = OllamaMistralTools()
            
            test_cases = [
                ("Hello, how are you?", "english"),
                ("Bonjour, comment allez-vous?", "french"),
                ("Hola, ¿cómo estás?", "spanish"),
                ("What fields do I have?", "english"),
                ("Combien d'eau pour CHlewi?", "french")
            ]
            
            all_passed = True
            for text, expected_lang in test_cases:
                detected = tools._detect_language(text)
                passed = detected == expected_lang
                self.log_test(f"Language Detection: {expected_lang}", passed, 
                            f"'{text}' -> {detected}")
                if not passed:
                    all_passed = False
            
            return all_passed
        except Exception as e:
            self.log_test("Language Detection", False, str(e))
            return False
    
    def test_tool_determination(self):
        """Test 6: Tool determination from questions."""
        try:
            tools = OllamaMistralTools()
            
            test_cases = [
                ("What time is it?", "get_current_time_date"),
                ("What fields do I have?", "get_all_user_areas_with_children"),
                ("Which fields need irrigation today?", "check_today_active_irrigation_user"),
                ("How much water does CHlewi need?", "check_irrigation_user_data"),
                ("Show me fields with lowest water volume", "get_lowest_soil_water_volume")
            ]
            
            all_passed = True
            for question, expected_tool in test_cases:
                result = tools._determine_tool_from_question(question)
                if result:
                    tool_name, args = result
                    passed = tool_name == expected_tool
                    self.log_test(f"Tool Determination: {expected_tool}", passed, 
                                f"'{question}' -> {tool_name}")
                else:
                    self.log_test(f"Tool Determination: {expected_tool}", False, 
                                f"No tool determined for '{question}'")
                    passed = False
                
                if not passed:
                    all_passed = False
            
            return all_passed
        except Exception as e:
            self.log_test("Tool Determination", False, str(e))
            return False
    
    def test_simple_ollama_request(self):
        """Test 7: Simple Ollama API request."""
        try:
            client = OllamaClient()
            messages = [
                {"role": "system", "content": "You are a helpful assistant. Respond briefly."},
                {"role": "user", "content": "Say hello in one word."}
            ]
            
            response = client.generate(messages=messages, temperature=0.1, max_tokens=50)
            
            if response and "message" in response:
                content = response["message"].get("content", "")
                passed = len(content.strip()) > 0
                self.log_test("Simple Ollama Request", passed, f"Response: '{content[:50]}...'")
                return passed
            else:
                self.log_test("Simple Ollama Request", False, "No valid response")
                return False
        except Exception as e:
            self.log_test("Simple Ollama Request", False, str(e))
            return False
    
    def test_ask_function_basic(self):
        """Test 8: Basic ask function."""
        try:
            question = "Hello, I'm testing the system"
            response = ask_ollama_mistral(question, user_id=self.test_user_id)
            
            passed = isinstance(response, str) and len(response.strip()) > 0
            self.log_test("Basic Ask Function", passed, f"Response length: {len(response)}")
            
            if passed:
                print(f"Sample response: {response[:100]}...")
            
            return passed
        except Exception as e:
            self.log_test("Basic Ask Function", False, str(e))
            return False
    
    def test_agricultural_question(self):
        """Test 9: Agricultural-specific question."""
        try:
            question = "What agricultural tools are available?"
            response = ask_ollama_mistral(question, user_id=self.test_user_id)
            
            # Check if response mentions agriculture or tools
            agricultural_keywords = ["agriculture", "farming", "irrigation", "field", "crop", "tool"]
            contains_ag_content = any(keyword.lower() in response.lower() for keyword in agricultural_keywords)
            
            passed = isinstance(response, str) and len(response.strip()) > 0 and contains_ag_content
            self.log_test("Agricultural Question", passed, 
                        f"Contains agricultural content: {contains_ag_content}")
            
            return passed
        except Exception as e:
            self.log_test("Agricultural Question", False, str(e))
            return False
    
    def test_multilingual_support(self):
        """Test 10: Multi-language support."""
        try:
            # Test French question
            french_question = "Bonjour, je suis un fermier"
            response = ask_ollama_mistral(french_question, user_id=self.test_user_id)
            
            passed = isinstance(response, str) and len(response.strip()) > 0
            self.log_test("Multilingual Support", passed, f"French response length: {len(response)}")
            
            return passed
        except Exception as e:
            self.log_test("Multilingual Support", False, str(e))
            return False
    
    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🧪 Starting Comprehensive Local Ollama Test Suite")
        print("=" * 60)
        
        start_time = time.time()
        
        # Core infrastructure tests
        print("\n📋 Infrastructure Tests:")
        self.test_ollama_connection()
        self.test_mistral_availability()
        self.test_ollama_client_initialization()
        
        # Component tests
        print("\n🔧 Component Tests:")
        self.test_tools_registration()
        self.test_language_detection()
        self.test_tool_determination()
        
        # Integration tests
        print("\n🔗 Integration Tests:")
        self.test_simple_ollama_request()
        self.test_ask_function_basic()
        self.test_agricultural_question()
        self.test_multilingual_support()
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.passed_tests + self.failed_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {(self.passed_tests / (self.passed_tests + self.failed_tests) * 100):.1f}%")
        print(f"Duration: {duration:.2f} seconds")
        
        if self.failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["passed"]:
                    print(f"  - {result['name']}: {result['message']}")
        
        # Save results to file
        self.save_results()
        
        return self.failed_tests == 0
    
    def save_results(self):
        """Save test results to a JSON file."""
        try:
            results = {
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "total_tests": self.passed_tests + self.failed_tests,
                    "passed": self.passed_tests,
                    "failed": self.failed_tests,
                    "success_rate": (self.passed_tests / (self.passed_tests + self.failed_tests) * 100) if (self.passed_tests + self.failed_tests) > 0 else 0
                },
                "test_results": self.test_results
            }
            
            with open("local_ollama_test_results.json", "w") as f:
                json.dump(results, f, indent=2)
            
            print(f"\n💾 Test results saved to: local_ollama_test_results.json")
        except Exception as e:
            print(f"⚠️ Could not save test results: {e}")

def main():
    """Main test execution."""
    test_suite = LocalOllamaTestSuite()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n🎉 All tests passed! Local Ollama implementation is ready to use.")
        return 0
    else:
        print("\n⚠️ Some tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
