from langchain_openai import OpenAIEmbeddings
from langchain_redis import RedisConfig, RedisVectorStore


class VectorStoreRetriever:
    def __init__(self, redis_url, index_name):
        self.redis_available = bool(redis_url)

        if not redis_url:
            print(f"⚠️ Redis URL not provided for {index_name}, vector store disabled")
            self.embeddings = None
            self.config = None
            self.vector_store = None
            return

        try:
            self.embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
            self.config = RedisConfig(
                index_name=index_name,
                redis_url=redis_url,
                metadata_schema=[{"name": "category", "type": "tag"}],
            )
            self.vector_store = RedisVectorStore(self.embeddings, config=self.config)
            print(f"✅ Vector store initialized for {index_name}")
        except Exception as e:
            print(f"⚠️ Failed to initialize vector store for {index_name}: {e}")
            self.redis_available = False
            self.embeddings = None
            self.config = None
            self.vector_store = None

    def load_and_embed(self, file_path):
        if not self.redis_available or not self.vector_store:
            print("⚠️ Vector store not available, cannot load and embed documents")
            return []

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # Split the content by the delimiter
            entries = content.split("*********")
            texts = []

            for entry in entries:
                entry = entry.strip()
                if entry:
                    parts = entry.split("Réponse:")
                    if len(parts) == 2:
                        question = parts[0].replace("Question:", "").strip()
                        answer = parts[1].strip()
                        texts.append(f"{question} {answer}")

            if not texts:
                raise ValueError("No valid question-answer pairs found in the file.")

            metadata = [{"category": "general"}] * len(texts)
            ids = self.vector_store.add_texts(texts, metadata)
            return ids
        except Exception as e:
            print(f"⚠️ Error loading and embedding documents: {e}")
            return []

    def lookup_document(self, query, k=2):
        if not self.redis_available or not self.vector_store:
            print("⚠️ Vector store not available, cannot perform document lookup")
            return []

        try:
            results = self.vector_store.similarity_search(query, k=k)
            return results
        except Exception as e:
            print(f"⚠️ Error performing document lookup: {e}")
            return []

