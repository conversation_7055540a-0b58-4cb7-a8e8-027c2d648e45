#!/usr/bin/env python3
"""
Interactive test script for local Ollama Mistral implementation.
This script allows manual testing and conversation with the agricultural assistant.
"""

import os
import sys
import time
from datetime import datetime

# Add the magonia directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'magonia'))

try:
    from magonia.local import ask_ollama_mistral, OllamaMistralTools
    print("✅ Successfully imported local modules")
except ImportError as e:
    print(f"❌ Failed to import local modules: {e}")
    sys.exit(1)

class InteractiveTestSession:
    """Interactive testing session for local Ollama implementation."""
    
    def __init__(self):
        self.user_id = "interactive_test_user"
        self.conversation_history = ""
        self.session_start = datetime.now()
        self.question_count = 0
        
    def check_prerequisites(self):
        """Check if Ollama and Mistral are available."""
        print("🔍 Checking prerequisites...")
        
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                model_names = [model['name'] for model in models]
                
                print(f"✅ Ollama is running with {len(models)} models")
                
                if any('mistral' in name.lower() for name in model_names):
                    print("✅ Mistral model is available")
                    return True
                else:
                    print("❌ Mistral model not found")
                    print("Available models:", model_names)
                    print("To install Mistral, run: ollama pull mistral")
                    return False
            else:
                print(f"❌ Ollama not responding (HTTP {response.status_code})")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to Ollama: {e}")
            print("Make sure Ollama is running with: ollama serve")
            return False
    
    def show_welcome(self):
        """Show welcome message and instructions."""
        print("\n" + "=" * 60)
        print("🌾 MAGONIA - Interactive Test Session")
        print("🤖 Local Ollama Mistral Agricultural Assistant")
        print("=" * 60)
        print("\n📋 Instructions:")
        print("• Type your agricultural questions in any language")
        print("• Type 'help' for sample questions")
        print("• Type 'stats' to see session statistics")
        print("• Type 'tools' to see available tools")
        print("• Type 'clear' to clear conversation history")
        print("• Type 'quit' or 'exit' to end the session")
        print("\n🚀 Ready to help with your farming needs!")
        print("-" * 60)
    
    def show_sample_questions(self):
        """Show sample questions for testing."""
        print("\n💡 Sample Questions to Try:")
        print("-" * 40)
        
        samples = [
            "🇺🇸 English:",
            "  • What fields do I have?",
            "  • Which fields need irrigation today?",
            "  • How much water does CHlewi need?",
            "  • What time is it now?",
            "  • Show me fields with lowest water volume",
            "",
            "🇫🇷 French:",
            "  • Bonjour, je suis un fermier",
            "  • Combien d'eau faut-il pour mes champs?",
            "  • Quels champs ont besoin d'irrigation?",
            "",
            "🇪🇸 Spanish:",
            "  • Hola, soy agricultor",
            "  • ¿Cuánta agua necesitan mis campos?",
            "",
            "🔧 Technical:",
            "  • Calculate irrigation for next 7 days",
            "  • Fields with highest evapotranspiration",
            "  • Total water consumption prediction"
        ]
        
        for sample in samples:
            print(sample)
    
    def show_available_tools(self):
        """Show available tools."""
        try:
            tools = OllamaMistralTools()
            print(f"\n🔧 Available Tools ({len(tools.tools)} total):")
            print("-" * 50)
            
            # Group tools by category
            categories = {
                "Time & Date": ["get_current_time_date", "get_tomorrow", "get_next_week", "get_next_month"],
                "Field Management": ["get_all_user_areas_with_children", "check_irrigation_user_data"],
                "Irrigation Analysis": ["check_today_active_irrigation_user", "check_irrigation_need_for_x_days", 
                                     "get_lowest_soil_water_volume", "calculate_total_irrigation_volume_next_x_days"],
                "Water Analysis": ["check_soil_water_volume", "fields_with_highest_water_requirements_for_x_days",
                                 "fields_with_optimal_soil_moisture_for_x_days"],
                "Predictions": ["fields_predicted_to_exceed_water_capacity_for_x_days", 
                              "predicted_water_consumption_rate_for_x_days"],
                "Documentation": ["lookup_document_tool"],
                "Memory": ["add_memory", "get_memories", "delete_memory", "clear_memories"]
            }
            
            for category, tool_list in categories.items():
                available_tools = [tool for tool in tool_list if tool in tools.tools]
                if available_tools:
                    print(f"\n📂 {category}:")
                    for tool in available_tools:
                        print(f"  • {tool}")
            
            # Show any uncategorized tools
            all_categorized = [tool for tools_list in categories.values() for tool in tools_list]
            uncategorized = [tool for tool in tools.tools.keys() if tool not in all_categorized]
            if uncategorized:
                print(f"\n📂 Other Tools:")
                for tool in uncategorized:
                    print(f"  • {tool}")
                    
        except Exception as e:
            print(f"❌ Error loading tools: {e}")
    
    def show_session_stats(self):
        """Show session statistics."""
        duration = datetime.now() - self.session_start
        print(f"\n📊 Session Statistics:")
        print("-" * 30)
        print(f"⏱️  Session duration: {duration}")
        print(f"❓ Questions asked: {self.question_count}")
        print(f"💬 Conversation length: {len(self.conversation_history)} chars")
        print(f"👤 User ID: {self.user_id}")
    
    def ask_question(self, question: str):
        """Ask a question and display the response."""
        if not question.strip():
            return
        
        self.question_count += 1
        print(f"\n🤔 You: {question}")
        print("🤖 Magonia: ", end="", flush=True)
        
        start_time = time.time()
        try:
            response = ask_ollama_mistral(
                question, 
                chat_history=self.conversation_history,
                user_id=self.user_id
            )
            end_time = time.time()
            
            print(response)
            
            # Update conversation history
            self.conversation_history += f"User: {question}\nAssistant: {response}\n"
            
            # Show response time
            response_time = end_time - start_time
            print(f"\n⏱️ Response time: {response_time:.2f}s")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            print("Please check if Ollama is running and try again.")
    
    def clear_conversation(self):
        """Clear conversation history."""
        self.conversation_history = ""
        self.question_count = 0
        print("🧹 Conversation history cleared!")
    
    def run_interactive_session(self):
        """Run the interactive testing session."""
        if not self.check_prerequisites():
            print("\n❌ Prerequisites not met. Please fix the issues above and try again.")
            return False
        
        self.show_welcome()
        
        while True:
            try:
                user_input = input(f"\n[{self.question_count + 1}] 🌾 Ask Magonia: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("\n👋 Thank you for testing Magonia! Goodbye!")
                    break
                elif user_input.lower() == 'help':
                    self.show_sample_questions()
                elif user_input.lower() == 'tools':
                    self.show_available_tools()
                elif user_input.lower() == 'stats':
                    self.show_session_stats()
                elif user_input.lower() == 'clear':
                    self.clear_conversation()
                else:
                    # Regular question
                    self.ask_question(user_input)
                    
            except KeyboardInterrupt:
                print("\n\n👋 Session interrupted. Goodbye!")
                break
            except EOFError:
                print("\n\n👋 Session ended. Goodbye!")
                break
            except Exception as e:
                print(f"\n❌ Unexpected error: {e}")
                print("Continuing session...")
        
        # Show final stats
        print("\n" + "=" * 60)
        self.show_session_stats()
        print("=" * 60)
        
        return True

def main():
    """Main interactive test execution."""
    print("🧪 Local Ollama Mistral - Interactive Test")
    
    session = InteractiveTestSession()
    success = session.run_interactive_session()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
